// localization.server.ts
type LocaleStrings = {
    [key: string]: string | { [key: string]: string };
};

type LocalizationData = {
    [locale: string]: LocaleStrings;
};

export async function getServerLocale(): Promise<string> {
    const headersModule = await import("next/headers");
    const headers = await headersModule.headers();
    const cookie = headers.get("cookie") || "";

    const match = cookie.match(/locale=([^;]+)/);
    if (match) return match[1];

    const acceptLang = headers.get("accept-language");
    if (acceptLang) {
        return acceptLang.split(",")[0].split("-")[0];
    }

    return "en";
}

// Global server-side missing translations store (Node.js global)
declare global {
    var __missingTranslations__:
        | Record<string, Record<string, string>>
        | undefined;
}

if (typeof global !== "undefined" && !global.__missingTranslations__) {
    global.__missingTranslations__ = {};
}

export async function useLocalization(namespace: string, locales: LocalizationData) {
    const locale = await getServerLocale();

    const t = (key: string, template?: Record<string, string | number>): string => {
        const keys = key.split(".");
        let current: any = locales[locale] || {};

        for (const k of keys) {
            current = current[k];
            if (current === undefined || current === null) {
                if (
                    process.env.NODE_ENV === "development"
                ) {
                    global.__missingTranslations__ ||= {};
                    global.__missingTranslations__[namespace] ||= {};
                    global.__missingTranslations__[namespace][key] ||= "";

                    console.warn(
                        `[i18n] Missing translation key "${key}" in namespace "${namespace}" for locale "${locale}"`
                    );
                }
                return key; // fallback
            }
        }

        if (typeof current === "string") {
            if (template) {
                return current.replace(/\{\{(\w+)\}\}/g, (_, match) => {
                    return template[match]?.toString() ?? `{${match}}`;
                });
            }
            return current;
        }

        return key;
    };

    return { t, locale };
}
