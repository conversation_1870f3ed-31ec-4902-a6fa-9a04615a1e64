'use client'

import { Tren<PERSON>Up, Pin } from 'lucide-react'
import { useLocalization } from '@/hooks/useLocalization/client'
import { locales } from './locales'
import { SidebarTabContent } from './SidebarTabs'
import { TableRowData } from './types'
import PageHeaderComponent from './header'
import TableComponent from './table'
import DataStats from './DataStats'
import { PinnedColumnManager } from './PinnedColumn'
import { DataPageEnhancedConfig } from './hooks/useDataPageLogic'

interface DataPageTabContentsProps<T = any> {
  config: DataPageEnhancedConfig<T>
  activeTab: string
  statsData: any
  isLoading: boolean
  pinnedColumns: string[]
  setPinnedColumns: (columns: string[]) => void
  listData: TableRowData[]
  totalItems: number
  currentPage: number
  pageSize: number
  totalPages: number
  onAdd: () => void
  onBulk?: () => void
  onEdit: (id: string) => void
  onDelete: (id: string) => void
  handleSearch: (query: string) => void
  handlePrint: () => void
  handleSortChange: (field: string, order: 'asc' | 'desc') => void
  handleFilterChange: (filters: string[]) => void
  handleDateFilterChange: (dateFilter: string) => void
  handlePageChange: (page: number) => void
}

export default function DataPageTabContents<T>({
  config,
  activeTab,
  statsData,
  isLoading,
  pinnedColumns,
  setPinnedColumns,
  listData,
  totalItems,
  currentPage,
  pageSize,
  totalPages,
  onAdd,
  onBulk,
  onEdit,
  onDelete,
  handleSearch,
  handlePrint,
  handleSortChange,
  handleFilterChange,
  handleDateFilterChange,
  handlePageChange
}: DataPageTabContentsProps<T>) {
  const { t } = useLocalization('crud-page', locales)

  return (
    <>
      {/* Stats Tab Content */}
      <SidebarTabContent value="stats" activeTab={activeTab} className="space-y-6 p-6">
        {statsData && (
          <DataStats
            data={statsData}
            isLoading={isLoading}
            title={"Statistics"}
          />  
        )}
      </SidebarTabContent>

      {/* Columns Tab Content */}
      <SidebarTabContent value="columns" activeTab={activeTab} className="space-y-6 p-6">
        <div className="max-w-md mx-auto">
          <div className="text-center mb-6">
            <Pin className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Column Management</h3>
            <p className="text-gray-600">Pin columns to keep them visible while scrolling horizontally</p>
          </div>

          <PinnedColumnManager
            headers={config.headers}
            pinnedColumns={pinnedColumns}
            onPinnedColumnsChange={setPinnedColumns}
          />
        </div>
      </SidebarTabContent>

      {/* Trends Tab Content */}
      <SidebarTabContent value="trends" activeTab={activeTab} className="space-y-6 p-6">
        <div className="text-center py-12">
          <TrendingUp className="w-16 h-16 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">{t('trends_coming_soon')}</h3>
          <p className="text-gray-600">{t('trends_description')}</p>
        </div>
      </SidebarTabContent>

      {/* Table Tab Content */}
      <SidebarTabContent value="table" activeTab={activeTab} className="space-y-6 p-6">
        <PageHeaderComponent
          title={config.title}
          total={totalItems}
          onAdd={onAdd}
          onBulk={config.bulkRoute ? onBulk : undefined}
          onSearch={handleSearch}
          onCetak={handlePrint}
          onSort={handleSortChange}
          sortOptions={config.sortOptions}
          dateFilterOptions={config.dateFilterOptions}
          filters={config.filters}
          onFilter={handleFilterChange}
          onDateFilter={handleDateFilterChange}
        />

        <TableComponent
          headers={config.headers}
          data={listData}
          action={{
            edit: true,
            delete: true
          }}
          onEdit={onEdit}
          onDelete={onDelete}
          columnWidth={config.columnWidths}
          defaultColumnWidth={config.defaultColumnWidth || "150px"}
          isScroll={true}
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          perPage={pageSize}
          pinnedColumns={pinnedColumns}
          isLoading={isLoading}
        />
      </SidebarTabContent>
    </>
  )
}
