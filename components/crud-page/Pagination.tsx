'use client'

import { <PERSON><PERSON>ef<PERSON>, ArrowRight } from 'lucide-react'
import { useLocalization } from '@/hooks/useLocalization/client'
import { locales } from './locales'

interface PaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
}

export default function Pagination({
  currentPage,
  totalPages,
  onPageChange
}: PaginationProps) {
  const { t } = useLocalization('crud-page', locales)

  const prevPage = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1)
    }
  }

  const nextPage = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1)
    }
  }

  if (totalPages <= 1) {
    return null
  }

  return (
    <div className="flex space-x-4 items-center mt-4 p-1 bg-gray-200 rounded-lg px-2 no-print">
      <button 
        onClick={prevPage} 
        className={`px-4 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 flex items-center ${
          currentPage === 1 ? 'hidden' : ''
        }`}
      >
        <ArrowLeft className="w-4 h-4" />
      </button>
      <span className="text-gray-700">
        {t('pagination_page')} {currentPage} {t('pagination_of')} {totalPages}
      </span>
      <button 
        onClick={nextPage} 
        className={`px-4 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 flex items-center ${
          currentPage === totalPages ? 'hidden' : ''
        }`}
      >
        <ArrowRight className="w-4 h-4" />
      </button>
    </div>
  )
}
