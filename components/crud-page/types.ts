import { ComponentType } from 'react'

// Common table types
export interface TableRowData {
  id: string
  columns: any[]
}

export interface ActionConfig {
  edit?: boolean
  delete?: boolean
}

export interface RowComponent {
  component: ComponentType<any>
  props?: Record<string, any>
}

// Header component types
export interface SortOption {
  value: string
  label: string
}

export interface DateFilterOption {
  value: string
  label: string
}

export interface Filter {
  id: string
  name: string
}

// New types for multiple filters and sorts
export interface FilterOption {
  id: string
  name: string
  type: 'text' | 'select' | 'date' | 'number'
  options?: Array<{ value: string; label: string }> // For select type filters
}

export interface MultipleFilter {
  id: string
  fieldId: string
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'greaterThan' | 'lessThan' | 'between'
  value: string | string[]
}

export interface MultipleSort {
  id: string
  field: string
  order: 'asc' | 'desc'
}

export interface PageHeaderProps {
  title: string
  total: number
  onAdd?: () => void
  onBulk?: () => void
  onSort?: (field: string, order: 'asc' | 'desc') => void
  onSearch?: (query: string) => void
  onCetak?: () => void
  sortOptions?: SortOption[]
  dateFilterOptions?: DateFilterOption[]
  cetakRef?: React.RefObject<HTMLElement>
  filters?: Filter[]
  onFilter?: (selectedFilters: string[]) => void
  onDateFilter?: (selectedDateFilter: string) => void

  // New props for enhanced functionality
  filterOptions?: FilterOption[] // Available filter fields for advanced filters
  multipleFilters?: MultipleFilter[] // Current active filters
  multipleSorts?: MultipleSort[] // Current active sorts
  onMultipleFiltersChange?: (filters: MultipleFilter[]) => void
  onMultipleSortsChange?: (sorts: MultipleSort[]) => void
}

// Table component types
export interface TableComponentProps {
  isScroll?: boolean
  headers: string[]
  data: TableRowData[]
  action?: ActionConfig
  selected?: { id: string }
  didSelectAt?: (id: string) => void
  onEdit?: (id: string) => void
  onDelete?: (id: string) => void
  rowComponents?: Record<string, RowComponent>
  columnWidth?: Record<string, string>
  defaultColumnWidth?: string
  currentPage?: number
  totalPages?: number
  onPageChange?: (page: number) => void
  perPage?: number
  pinnedColumns?: string[] // Array of column names to pin to the left
  isLoading?: boolean // Loading state for shimmer effect
}

// Sample use component types
export interface Pasien {
  name: string
  no_rekam_medik: string
}

export interface RawatJalan {
  no_rawat: string
}

export interface Radiologi {
  kode: string
  name: string
}

export interface LaboratoriumData {
  id: string
  pasien: Pasien
  rawat_jalan: RawatJalan
  radiologi: Radiologi
}

export interface ApiResponse {
  data: LaboratoriumData[]
}
