'use client'

import SidebarTabs from './SidebarTabs'
import DataPageTabContents from './DataPageTabContents'
import { useDataPageLogic } from './hooks/useDataPageLogic'
import type { DataPageEnhancedConfig } from './hooks/useDataPageLogic'

// Re-export the config type for external use
export type { DataPageEnhancedConfig } from './hooks/useDataPageLogic'

interface DataPageEnhancedProps<T> {
  config: DataPageEnhancedConfig<T>
}

export default function DataPageEnhanced<T>({ config }: DataPageEnhancedProps<T>) {
  const {
    // State
    listData,
    isLoading,
    totalItems,
    currentPage,
    pageSize,
    totalPages,
    pinnedColumns,
    setPinnedColumns,
    activeTab,
    setActiveTab,
    statsData,

    // Handlers
    onAdd,
    onEdit,
    onBulk,
    handleSearch,
    handleFilterChange,
    handleDateFilterChange,
    handleSortChange,
    handlePageChange,
    handleDelete,
    handlePrint
  } = useDataPageLogic(config)

  return (
    <div className="flex flex-col w-full h-full relative">
      {/* Page Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">{config.title}</h1>
        {config.subtitle && (
          <p className="text-gray-600">{config.subtitle}</p>
        )}
      </div>

      {/* Sidebar Tabs */}
      <SidebarTabs activeTab={activeTab} onTabChange={setActiveTab}>
        <DataPageTabContents
          config={config}
          activeTab={activeTab}
          statsData={statsData}
          isLoading={isLoading}
          pinnedColumns={pinnedColumns}
          setPinnedColumns={setPinnedColumns}
          listData={listData}
          totalItems={totalItems}
          currentPage={currentPage}
          pageSize={pageSize}
          totalPages={totalPages}
          onAdd={onAdd}
          onBulk={onBulk}
          onEdit={onEdit}
          onDelete={handleDelete}
          handleSearch={handleSearch}
          handlePrint={handlePrint}
          handleSortChange={handleSortChange}
          handleFilterChange={handleFilterChange}
          handleDateFilterChange={handleDateFilterChange}
          handlePageChange={handlePageChange}
        />
      </SidebarTabs>
    </div>
  )
}
