'use client'

import { useState, useEffect } from 'react'
import { toast } from '@/hooks/use-toast'
import { useLocalization } from '@/hooks/useLocalization/client'
import { locales } from './locales'

// Import components
import InstructionsSection from './bulk-delete/InstructionsSection'
import SelectionSection from './bulk-delete/SelectionSection'
import DeleteConfirmationDialog from './bulk-delete/DeleteConfirmationDialog'
import DeleteResultsSection from './bulk-delete/DeleteResultsSection'

// Import types
import { BulkDeleteConfig, DeleteResult } from './bulk-delete/types'

// Re-export types for backward compatibility
export type { BulkDeleteConfig, DeleteResult } from './bulk-delete/types'

interface BulkDeleteComponentProps {
  config: BulkDeleteConfig
  onSuccess?: () => void
}

export default function BulkDeleteComponent({ config, onSuccess }: BulkDeleteComponentProps) {
  const { t } = useLocalization('crud-page', locales)

  // State
  const [data, setData] = useState<Record<string, any>[]>([])
  const [selectedIds, setSelectedIds] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [deleteResult, setDeleteResult] = useState<DeleteResult | null>(null)

  // Load data
  const loadData = async () => {
    setIsLoading(true)
    try {
      const fetchedData = await config.fetchData()
      setData(fetchedData)
    } catch (error) {
      console.error('Error loading data:', error)
      toast({
        title: t('error_title'),
        description: t('load_error'),
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Handle row selection
  const handleRowSelect = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedIds(prev => [...prev, id])
    } else {
      setSelectedIds(prev => prev.filter(selectedId => selectedId !== id))
    }
  }

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedIds(data.map(item => item.id))
    } else {
      setSelectedIds([])
    }
  }

  // Execute bulk delete
  const executeBulkDelete = async () => {
    if (selectedIds.length === 0) return

    setIsDeleting(true)
    setDeleteResult(null)

    try {
      const result = await config.deleteData(selectedIds)
      setDeleteResult(result)

      if (result.successful > 0) {
        toast({
          title: 'Delete Complete',
          description: `${result.successful} ${config.itemNamePlural} deleted successfully`,
          variant: result.failed > 0 ? 'destructive' : 'default'
        })

        // Refresh data and clear selection
        setSelectedIds([])
        await loadData()

        if (onSuccess) {
          onSuccess()
        }
      }

      if (result.failed > 0) {
        toast({
          title: 'Partial Success',
          description: `${result.failed} ${config.itemNamePlural} could not be deleted`,
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Delete error:', error)
      toast({
        title: t('error_title'),
        description: 'Delete operation failed. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setIsDeleting(false)
    }
  }

  // Get selected items for display
  const selectedItems = data.filter(item => selectedIds.includes(item.id))

  // Load data on component mount
  useEffect(() => {
    loadData()
  }, [])

  return (
    <div className="space-y-6">
      <InstructionsSection config={config} />

      <SelectionSection
        config={config}
        data={data}
        selectedIds={selectedIds}
        isLoading={isLoading}
        onRowSelect={handleRowSelect}
        onSelectAll={handleSelectAll}
      >
        <DeleteConfirmationDialog
          config={config}
          selectedIds={selectedIds}
          selectedItems={selectedItems}
          isDeleting={isDeleting}
          onConfirm={executeBulkDelete}
        />
      </SelectionSection>

      <DeleteResultsSection deleteResult={deleteResult} />
    </div>
  )
}
