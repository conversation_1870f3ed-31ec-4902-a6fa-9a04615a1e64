'use client'

import { useRef } from 'react'
import { Plus, Minus, Printer } from 'lucide-react'
import { useLocalization } from '@/hooks/useLocalization/client'
import { locales } from '../locales'
import { FilterOption, SortOption, MultipleFilter, MultipleSort } from '../types'

interface AdvancedFiltersProps {
  filterOptions?: FilterOption[] // Available filter fields
  sortOptions?: SortOption[]
  multipleFilters: MultipleFilter[] // Current active filters
  multipleSorts: MultipleSort[] // Current active sorts
  onAddFilter: () => void
  onRemoveFilter: (filterId: string) => void
  onUpdateFilter: (filterId: string, field: keyof MultipleFilter, value: any) => void
  onAddSort: () => void
  onRemoveSort: (sortId: string) => void
  onUpdateSort: (sortId: string, field: keyof MultipleSort, value: any) => void
  onCetak?: () => void
  cetakRef?: React.RefObject<HTMLElement>
  title: string
}

export default function AdvancedFilters({
  filterOptions,
  sortOptions,
  multipleFilters,
  multipleSorts,
  onAddFilter,
  onRemoveFilter,
  onUpdateFilter,
  onAddSort,
  onRemoveSort,
  onUpdateSort,
  onCetak,
  cetakRef,
  title
}: AdvancedFiltersProps) {
  const { t } = useLocalization('crud-page', locales)
  const containerRef = useRef<HTMLDivElement>(null)

  const getOperatorOptions = (filterType: string) => {
    switch (filterType) {
      case 'text':
        return [
          { value: 'contains', label: t('contains') || 'Contains' },
          { value: 'equals', label: t('equals') || 'Equals' },
          { value: 'startsWith', label: t('starts_with') || 'Starts with' },
          { value: 'endsWith', label: t('ends_with') || 'Ends with' }
        ]
      case 'number':
      case 'date':
        return [
          { value: 'equals', label: t('equals') || 'Equals' },
          { value: 'greaterThan', label: t('greater_than') || 'Greater than' },
          { value: 'lessThan', label: t('less_than') || 'Less than' },
          { value: 'between', label: t('between') || 'Between' }
        ]
      case 'select':
        return [
          { value: 'equals', label: t('equals') || 'Equals' }
        ]
      default:
        return [
          { value: 'equals', label: t('equals') || 'Equals' }
        ]
    }
  }

  const handleCetak = () => {
    if (onCetak) {
      onCetak()
    } else if (cetakRef?.current) {
      // Clone the element to avoid modifying the original
      const clone = cetakRef.current.cloneNode(true) as HTMLElement

      // Remove elements with class 'no-print'
      const noPrintElements = clone.querySelectorAll('.no-print')
      noPrintElements.forEach(element => element.remove())

      // Remove all inline styles
      const elementsWithStyle = clone.querySelectorAll('[style]')
      elementsWithStyle.forEach(element => {
        element.removeAttribute('style')
      })

      // Remove all classes
      const elementsWithClass = clone.querySelectorAll('[class]')
      elementsWithClass.forEach(element => {
        element.removeAttribute('class')
      })

      // Generate HTML for the print window
      const tableHtml = clone.outerHTML
      const printWindow = window.open('', '', 'height=800,width=1000')
      if (printWindow) {
        printWindow.document.write(`<html><head><title>${title}</title>`)
        printWindow.document.write('<style>table { width: 100%; border-collapse: collapse; } th, td { border: 1px solid black; padding: 8px; } th { background-color: #f4f4f4; }</style>')
        printWindow.document.write('</head><body>')
        printWindow.document.write(tableHtml)
        printWindow.document.write('</body></html>')
        printWindow.document.close()
        printWindow.focus()
        printWindow.print()
      }
    } else {
      alert(t('print_not_available'))
    }
  }

  return (
    <div className="mb-3 p-4 bg-gray-50 rounded-lg border" ref={containerRef}>
      <div className="space-y-4">
        {/* Multiple Filters Section */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-700">{t('filters') || 'Filters'}</h3>
            <button
              onClick={onAddFilter}
              className="flex items-center gap-1 px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              <Plus className="w-3 h-3" />
              {t('add_filter') || 'Add Filter'}
            </button>
          </div>

          {multipleFilters.length === 0 ? (
            <p className="text-sm text-gray-500">{t('no_filters_added') || 'No filters added'}</p>
          ) : (
            <div className="space-y-2">
              {multipleFilters.map((filter) => {
                const selectedFilterOption = filterOptions?.find(opt => opt.id === filter.fieldId)
                const operatorOptions = selectedFilterOption ? getOperatorOptions(selectedFilterOption.type) : []

                return (
                  <div key={filter.id} className="flex items-center gap-2 p-2 bg-white rounded border">
                    {/* Field Selection */}
                    <select
                      className="h-8 w-32 bg-white rounded border text-xs px-2"
                      value={filter.fieldId}
                      onChange={(e) => onUpdateFilter(filter.id, 'fieldId', e.target.value)}
                    >
                      <option value="">{t('select_field') || 'Select Field'}</option>
                      {filterOptions?.map((option) => (
                        <option key={option.id} value={option.id}>
                          {option.name}
                        </option>
                      ))}
                    </select>

                    {/* Operator Selection */}
                    <select
                      className="h-8 w-24 bg-white rounded border text-xs px-2"
                      value={filter.operator}
                      onChange={(e) => onUpdateFilter(filter.id, 'operator', e.target.value)}
                      disabled={!filter.fieldId}
                    >
                      {operatorOptions.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>

                    {/* Value Input */}
                    {selectedFilterOption?.type === 'select' ? (
                      <select
                        className="h-8 w-32 bg-white rounded border text-xs px-2"
                        value={Array.isArray(filter.value) ? filter.value[0] || '' : filter.value}
                        onChange={(e) => onUpdateFilter(filter.id, 'value', e.target.value)}
                        disabled={!filter.fieldId}
                      >
                        <option value="">{t('select_value') || 'Select Value'}</option>
                        {selectedFilterOption.options?.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    ) : (
                      <input
                        type={selectedFilterOption?.type === 'number' ? 'number' : selectedFilterOption?.type === 'date' ? 'date' : 'text'}
                        className="h-8 w-32 bg-white rounded border text-xs px-2"
                        placeholder={t('enter_value') || 'Enter value'}
                        value={Array.isArray(filter.value) ? filter.value[0] || '' : filter.value}
                        onChange={(e) => onUpdateFilter(filter.id, 'value', e.target.value)}
                        disabled={!filter.fieldId}
                      />
                    )}

                    {/* Remove Button */}
                    <button
                      onClick={() => onRemoveFilter(filter.id)}
                      className="flex items-center justify-center w-6 h-6 text-red-500 hover:bg-red-50 rounded"
                    >
                      <Minus className="w-3 h-3" />
                    </button>
                  </div>
                )
              })}
            </div>
          )}
        </div>

        {/* Multiple Sorts Section */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-700">{t('sorting') || 'Sorting'}</h3>
            <button
              onClick={onAddSort}
              className="flex items-center gap-1 px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600"
            >
              <Plus className="w-3 h-3" />
              {t('add_sort') || 'Add Sort'}
            </button>
          </div>

          {multipleSorts.length === 0 ? (
            <p className="text-sm text-gray-500">{t('no_sorts_added') || 'No sorting added'}</p>
          ) : (
            <div className="space-y-2">
              {multipleSorts.map((sort, index) => (
                <div key={sort.id} className="flex items-center gap-2 p-2 bg-white rounded border">
                  {/* Priority indicator */}
                  <span className="text-xs text-gray-500 w-4">{index + 1}.</span>

                  {/* Field Selection */}
                  <select
                    className="h-8 w-40 bg-white rounded border text-xs px-2"
                    value={sort.field}
                    onChange={(e) => onUpdateSort(sort.id, 'field', e.target.value)}
                  >
                    <option value="">{t('select_column') || 'Select Column'}</option>
                    {sortOptions?.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>

                  {/* Order Selection */}
                  <select
                    className="h-8 w-20 bg-white rounded border text-xs px-2"
                    value={sort.order}
                    onChange={(e) => onUpdateSort(sort.id, 'order', e.target.value as 'asc' | 'desc')}
                    disabled={!sort.field}
                  >
                    <option value="asc">{t('asc_order') || 'Asc'}</option>
                    <option value="desc">{t('desc_order') || 'Desc'}</option>
                  </select>

                  {/* Remove Button */}
                  <button
                    onClick={() => onRemoveSort(sort.id)}
                    className="flex items-center justify-center w-6 h-6 text-red-500 hover:bg-red-50 rounded"
                  >
                    <Minus className="w-3 h-3" />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Print Button */}
        {(onCetak || cetakRef) && (
          <div className="flex justify-end">
            <button
              className="h-9 bg-white rounded-[10px] border flex items-center px-3 gap-2 hover:bg-gray-50"
              onClick={handleCetak}
            >
              <Printer className="w-4 h-4" />
              <span>{t('print_button')}</span>
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
