'use client'

import { useRef, useEffect } from 'react'
import { ChevronDown, Printer } from 'lucide-react'
import { useLocalization } from '@/hooks/useLocalization/client'
import { locales } from '../locales'
import { DateFilterOption, FilterOption, SortOption } from '../types'

interface AdvancedFiltersProps {
  dateFilterOptions?: DateFilterOption[]
  filters?: FilterOption[]
  sortOptions?: SortOption[]
  selectedDateFilter: string | null
  selectedFilters: string[]
  selectedSortField: string
  selectedSortOrder: 'asc' | 'desc'
  showDateFilterDropdown: boolean
  showFilterDropdown: boolean
  onToggleDateFilter: () => void
  onToggleFilter: () => void
  onDateFilterChange: (option: DateFilterOption) => void
  onFilterChange: (filterId: string) => void
  onSortFieldChange: (event: React.ChangeEvent<HTMLSelectElement>) => void
  onSortOrderChange: (event: React.ChangeEvent<HTMLSelectElement>) => void
  onCetak?: () => void
  cetakRef?: React.RefObject<HTMLElement>
  title: string
}

export default function AdvancedFilters({
  dateFilterOptions,
  filters,
  sortOptions,
  selectedDateFilter,
  selectedFilters,
  selectedSortField,
  selectedSortOrder,
  showDateFilterDropdown,
  showFilterDropdown,
  onToggleDateFilter,
  onToggleFilter,
  onDateFilterChange,
  onFilterChange,
  onSortFieldChange,
  onSortOrderChange,
  onCetak,
  cetakRef,
  title
}: AdvancedFiltersProps) {
  const { t } = useLocalization('crud-page', locales)
  const containerRef = useRef<HTMLDivElement>(null)

  const filterLabel = (() => {
    if (selectedFilters.length === 0) {
      return t('select_filter')
    } else if (selectedFilters.length === 1) {
      return filters?.find(f => f.id === selectedFilters[0])?.name || t('select_filter')
    } else {
      return `${filters?.find(f => f.id === selectedFilters[0])?.name || t('select_filter')} (+${selectedFilters.length - 1})`
    }
  })()

  const selectedDateFilterLabel = (() => {
    return dateFilterOptions?.find(option => option.value === selectedDateFilter)?.label || t('select_date_range')
  })()

  const handleCetak = () => {
    if (onCetak) {
      onCetak()
    } else if (cetakRef?.current) {
      // Clone the element to avoid modifying the original
      const clone = cetakRef.current.cloneNode(true) as HTMLElement

      // Remove elements with class 'no-print'
      const noPrintElements = clone.querySelectorAll('.no-print')
      noPrintElements.forEach(element => element.remove())

      // Remove all inline styles
      const elementsWithStyle = clone.querySelectorAll('[style]')
      elementsWithStyle.forEach(element => {
        element.removeAttribute('style')
      })

      // Remove all classes
      const elementsWithClass = clone.querySelectorAll('[class]')
      elementsWithClass.forEach(element => {
        element.removeAttribute('class')
      })

      // Generate HTML for the print window
      const tableHtml = clone.outerHTML
      const printWindow = window.open('', '', 'height=800,width=1000')
      if (printWindow) {
        printWindow.document.write(`<html><head><title>${title}</title>`)
        printWindow.document.write('<style>table { width: 100%; border-collapse: collapse; } th, td { border: 1px solid black; padding: 8px; } th { background-color: #f4f4f4; }</style>')
        printWindow.document.write('</head><body>')
        printWindow.document.write(tableHtml)
        printWindow.document.write('</body></html>')
        printWindow.document.close()
        printWindow.focus()
        printWindow.print()
      }
    } else {
      alert(t('print_not_available'))
    }
  }

  const handleClickOutside = (event: MouseEvent) => {
    if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
      // Close dropdowns when clicking outside
    }
  }

  useEffect(() => {
    document.addEventListener('click', handleClickOutside)
    return () => {
      document.removeEventListener('click', handleClickOutside)
    }
  }, [])

  return (
    <div className="mb-3 p-4 bg-gray-50 rounded-lg border" ref={containerRef}>
      <div className="flex flex-wrap items-center gap-2">
        {/* Date Filter Dropdown */}
        {dateFilterOptions && dateFilterOptions.length > 0 && (
          <div className="relative">
            <button 
              className="h-9 w-48 bg-white rounded border flex justify-between items-center px-3" 
              onClick={onToggleDateFilter}
            >
              <span>{selectedDateFilterLabel}</span>
              <ChevronDown className="w-6 h-6 ml-2" />
            </button>
            {showDateFilterDropdown && (
              <div className="absolute bg-white border rounded shadow-md mt-2 w-full z-30">
                {dateFilterOptions.map((option) => (
                  <button 
                    key={option.value} 
                    className="flex items-center p-2 hover:bg-gray-300 w-full" 
                    onClick={() => onDateFilterChange(option)}
                  >
                    <input 
                      type="radio" 
                      id={option.value} 
                      value={option.value} 
                      checked={selectedDateFilter === option.value}
                      readOnly
                    />
                    <label htmlFor={option.value} className="ml-2">{option.label}</label>
                  </button>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Filter Dropdown */}
        {filters && filters.length > 0 && (
          <div className="relative">
            <button 
              className="h-9 w-48 bg-white rounded border flex justify-between items-center px-3" 
              onClick={onToggleFilter}
            >
              <span>{filterLabel}</span>
              <ChevronDown className="w-6 h-6 ml-2" />
            </button>
            {showFilterDropdown && (
              <div className="absolute bg-white border rounded shadow-md mt-2 w-full z-30">
                {filters.map((filter) => (
                  <div key={filter.id} className="flex items-center p-2 hover:bg-gray-300">
                    <input 
                      type="checkbox" 
                      id={filter.id} 
                      value={filter.id} 
                      checked={selectedFilters.includes(filter.id)}
                      onChange={() => onFilterChange(filter.id)} 
                    />
                    <label htmlFor={filter.id} className="ml-2 w-full">{filter.name}</label>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Sort Field */}
        {sortOptions && sortOptions.length > 0 && (
          <div>
            <select className="h-9 w-48 bg-white rounded border" onChange={onSortFieldChange} value={selectedSortField}>
              <option value="">{t('select_column')}</option>
              {sortOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Sort Order */}
        {sortOptions && sortOptions.length > 0 && (
          <div>
            <select className="h-9 w-16 bg-white rounded border" onChange={onSortOrderChange} value={selectedSortOrder}>
              <option value="asc">{t('asc_order')}</option>
              <option value="desc">{t('desc_order')}</option>
            </select>
          </div>
        )}

        {/* Print Button */}
        {(onCetak || cetakRef) && (
          <div>
            <button 
              className="h-9 bg-white rounded-[10px] border flex items-center px-3 gap-2" 
              onClick={handleCetak}
            >
              <Printer className="w-4 h-4" />
              <span>{t('print_button')}</span>
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
