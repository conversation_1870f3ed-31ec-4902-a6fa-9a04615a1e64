'use client'

import { Search, Plus, Settings, MoreHorizontal } from 'lucide-react'
import { useLocalization } from '@/hooks/useLocalization/client'
import { locales } from '../locales'

interface SimpleHeaderProps {
  title: string
  total: number
  searchQuery: string
  onSearch?: (query: string) => void
  onAdd?: () => void
  onBulk?: () => void
  onToggleAdvanced: () => void
  showAdvancedToggle: boolean
  onSearchChange: (event: React.ChangeEvent<HTMLInputElement>) => void
}

export default function SimpleHeader({
  title,
  total,
  searchQuery,
  onSearch,
  onAdd,
  onBulk,
  onToggleAdvanced,
  showAdvancedToggle,
  onSearchChange
}: SimpleHeaderProps) {
  const { t } = useLocalization('crud-page', locales)

  return (
    <div className="flex flex-col">
      {/* Title */}
      <h2 className="text-black text-[22px] font-bold font-['DM Sans'] tracking-tight">{title}</h2>
      
      {/* Simple Header Row */}
      <div className="mb-3 flex justify-between items-center">
        <div className="text-black text-[22px] font-medium font-['DM Sans'] tracking-tight">
          {t('total_label')}: {total}
        </div>

        <div className="flex items-center gap-2">
          {/* Search Input */}
          {onSearch && (
            <div className="relative">
              <input 
                className="h-9 w-96 p-3 bg-white rounded border" 
                type="text" 
                placeholder={t('search_placeholder')}
                onChange={onSearchChange}
                value={searchQuery} 
              />
              <button 
                className="absolute right-2 top-2 text-gray-500" 
                onClick={() => onSearchChange({ target: { value: searchQuery } } as any)}
              >
                <Search className="w-6 h-6" />
              </button>
            </div>
          )}

          {/* Add Button */}
          {onAdd && (
            <button
              className="h-9 bg-blue-500 text-white rounded-[10px] border flex items-center px-3 gap-2"
              onClick={onAdd}
            >
              <Plus className="w-4 h-4" />
              <span>{t('add_button')}</span>
            </button>
          )}

          {/* Bulk Operations Button */}
          {onBulk && (
            <button
              className="h-9 bg-purple-500 text-white rounded-[10px] border flex items-center px-3 gap-2"
              onClick={onBulk}
            >
              <Settings className="w-4 h-4" />
              <span>{t('bulk_ops_button')}</span>
            </button>
          )}

          {/* More Button - Toggle Advanced Filters */}
          {showAdvancedToggle && (
            <button
              className="h-9 bg-gray-100 hover:bg-gray-200 rounded-[10px] border flex items-center px-3 gap-2"
              onClick={onToggleAdvanced}
            >
              <MoreHorizontal className="w-4 h-4" />
              <span>{t('more_button')}</span>
            </button>
          )}
        </div>
      </div>
    </div>
  )
}
