'use client'

import { useState, useRef, useEffect } from 'react'
import { Search, Plus, Settings, MoreHorizontal, ChevronDown } from 'lucide-react'
import { useLocalization } from '@/hooks/useLocalization/client'
import { locales } from '../locales'
import { DateFilterOption, SortOption } from '../types'

interface SimpleHeaderProps {
  title: string
  total: number
  searchQuery: string
  onSearch?: (query: string) => void
  onAdd?: () => void
  onBulk?: () => void
  onToggleAdvanced: () => void
  showAdvancedToggle: boolean
  onSearchChange: (event: React.ChangeEvent<HTMLInputElement>) => void
  // New props for date filter and sorting
  dateFilterOptions?: DateFilterOption[]
  sortOptions?: SortOption[]
  selectedDateFilter?: string | null
  selectedSortField?: string
  selectedSortOrder?: 'asc' | 'desc'
  onDateFilterChange?: (option: DateFilterOption) => void
  onSortFieldChange?: (field: string) => void
  onSortOrderChange?: (order: 'asc' | 'desc') => void
}

export default function SimpleHeader({
  title,
  total,
  searchQuery,
  onSearch,
  onAdd,
  onBulk,
  onToggleAdvanced,
  showAdvancedToggle,
  onSearchChange,
  dateFilterOptions,
  sortOptions,
  selectedDateFilter,
  selectedSortField,
  selectedSortOrder,
  onDateFilterChange,
  onSortFieldChange,
  onSortOrderChange
}: SimpleHeaderProps) {
  const { t } = useLocalization('crud-page', locales)
  const [showDateFilterDropdown, setShowDateFilterDropdown] = useState(false)
  const dateFilterRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dateFilterRef.current && !dateFilterRef.current.contains(event.target as Node)) {
        setShowDateFilterDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const selectedDateFilterLabel = (() => {
    return dateFilterOptions?.find(option => option.value === selectedDateFilter)?.label || t('select_date_range')
  })()

  return (
    <div className="flex flex-col">
      {/* Title */}
      <h2 className="text-black text-[22px] font-bold font-['DM Sans'] tracking-tight">{title}</h2>

      {/* Simple Header Row */}
      <div className="mb-3 flex justify-between items-center">
        <div className="text-black text-[22px] font-medium font-['DM Sans'] tracking-tight">
          {t('total_label')}: {total}
        </div>

        <div className="flex items-center gap-2">
          {/* Search Input */}
          {onSearch && (
            <div className="relative">
              <input
                className="h-9 w-96 p-3 bg-white rounded border"
                type="text"
                placeholder={t('search_placeholder')}
                onChange={onSearchChange}
                value={searchQuery}
              />
              <button
                className="absolute right-2 top-2 text-gray-500"
                onClick={() => onSearchChange({ target: { value: searchQuery } } as any)}
              >
                <Search className="w-6 h-6" />
              </button>
            </div>
          )}

          {/* Date Filter Dropdown */}
          {dateFilterOptions && dateFilterOptions.length > 0 && (
            <div className="relative" ref={dateFilterRef}>
              <button
                className="h-9 w-48 bg-white rounded border flex justify-between items-center px-3"
                onClick={() => setShowDateFilterDropdown(!showDateFilterDropdown)}
              >
                <span className="text-sm">{selectedDateFilterLabel}</span>
                <ChevronDown className="w-4 h-4 ml-2" />
              </button>
              {showDateFilterDropdown && (
                <div className="absolute bg-white border rounded shadow-md mt-2 w-full z-30">
                  {dateFilterOptions.map((option) => (
                    <button
                      key={option.value}
                      className="flex items-center p-2 hover:bg-gray-100 w-full text-left"
                      onClick={() => {
                        onDateFilterChange?.(option)
                        setShowDateFilterDropdown(false)
                      }}
                    >
                      <input
                        type="radio"
                        id={option.value}
                        value={option.value}
                        checked={selectedDateFilter === option.value}
                        readOnly
                      />
                      <label htmlFor={option.value} className="ml-2 text-sm">{option.label}</label>
                    </button>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Sort Field */}
          {sortOptions && sortOptions.length > 0 && (
            <div>
              <select
                className="h-9 w-40 bg-white rounded border text-sm px-2"
                onChange={(e) => onSortFieldChange?.(e.target.value)}
                value={selectedSortField || ''}
              >
                <option value="">{t('select_column')}</option>
                {sortOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Sort Order */}
          {sortOptions && sortOptions.length > 0 && selectedSortField && (
            <div>
              <select
                className="h-9 w-20 bg-white rounded border text-sm px-2"
                onChange={(e) => onSortOrderChange?.(e.target.value as 'asc' | 'desc')}
                value={selectedSortOrder || 'asc'}
              >
                <option value="asc">{t('asc_order')}</option>
                <option value="desc">{t('desc_order')}</option>
              </select>
            </div>
          )}

          {/* Add Button */}
          {onAdd && (
            <button
              className="h-9 bg-blue-500 text-white rounded-[10px] border flex items-center px-3 gap-2"
              onClick={onAdd}
            >
              <Plus className="w-4 h-4" />
              <span>{t('add_button')}</span>
            </button>
          )}

          {/* Bulk Operations Button */}
          {onBulk && (
            <button
              className="h-9 bg-purple-500 text-white rounded-[10px] border flex items-center px-3 gap-2"
              onClick={onBulk}
            >
              <Settings className="w-4 h-4" />
              <span>{t('bulk_ops_button')}</span>
            </button>
          )}

          {/* More Button - Toggle Advanced Filters */}
          {showAdvancedToggle && (
            <button
              className="h-9 bg-gray-100 hover:bg-gray-200 rounded-[10px] border flex items-center px-3 gap-2"
              onClick={onToggleAdvanced}
            >
              <MoreHorizontal className="w-4 h-4" />
              <span>{t('more_button')}</span>
            </button>
          )}
        </div>
      </div>
    </div>
  )
}
