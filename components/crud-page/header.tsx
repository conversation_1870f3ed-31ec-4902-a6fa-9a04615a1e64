'use client'

import { useState, useEffect, useCallback } from 'react'
import SimpleHeader from './header/SimpleHeader'
import AdvancedFilters from './header/AdvancedFilters'

// Simple debounce function
function debounce<T extends (...args: any[]) => any>(func: T, delay: number): T {
  let timeoutId: NodeJS.Timeout
  return ((...args: any[]) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }) as T
}

import { PageHeaderProps, DateFilterOption } from './types'

export default function PageHeaderComponent({
  title,
  total,
  onAdd,
  onBulk,
  onSort,
  onSearch,
  onCetak,
  sortOptions,
  dateFilterOptions,
  cetakRef,
  filters,
  onFilter,
  onDateFilter
}: PageHeaderProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedSortField, setSelectedSortField] = useState('')
  const [selectedSortOrder, setSelectedSortOrder] = useState<'asc' | 'desc'>('asc')
  const [selectedFilters, setSelectedFilters] = useState<string[]>([])
  const [selectedDateFilter, setSelectedDateFilter] = useState<string | null>(null)
  const [showFilterDropdown, setShowFilterDropdown] = useState(false)
  const [showDateFilterDropdown, setShowDateFilterDropdown] = useState(false)
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)

  const debounceDelay = 300

  // Create debounced search function
  const debouncedSearch = useCallback(
    debounce((query: string) => {
      if (onSearch) {
        onSearch(query)
      }
    }, debounceDelay),
    [onSearch]
  )

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value
    setSearchQuery(value)
    debouncedSearch(value)
  }

  const handleSortField = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const value = event.target.value
    setSelectedSortField(value)
    applySorting(value, selectedSortOrder)
  }

  const handleSortOrder = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const value = event.target.value as 'asc' | 'desc'
    setSelectedSortOrder(value)
    applySorting(selectedSortField, value)
  }

  const applySorting = (field: string, order: 'asc' | 'desc') => {
    if (field && onSort) {
      onSort(field, order)
    }
  }

  const toggleFilterDropdown = () => {
    setShowFilterDropdown(!showFilterDropdown)
  }

  const handleFilters = (filterId: string) => {
    const newSelectedFilters = selectedFilters.includes(filterId)
      ? selectedFilters.filter(id => id !== filterId)
      : [...selectedFilters, filterId]

    setSelectedFilters(newSelectedFilters)

    if (newSelectedFilters.length > 0 && onFilter) {
      onFilter(newSelectedFilters)
    }
  }

  const toggleDateFilterDropdown = () => {
    setShowDateFilterDropdown(!showDateFilterDropdown)
  }

  const handleDateFilterChange = (option: DateFilterOption) => {
    setSelectedDateFilter(option.value)
    setShowDateFilterDropdown(false)
    applyDateFilter(option.value)
  }

  const applyDateFilter = (value: string) => {
    if (value && onDateFilter) {
      onDateFilter(value)
    }
  }

  // Determine if we should show the advanced toggle button
  const showAdvancedToggle = Boolean(
    (dateFilterOptions && dateFilterOptions.length > 0) ||
    (filters && filters.length > 0) ||
    (sortOptions && sortOptions.length > 0) ||
    (onCetak || cetakRef)
  )

  useEffect(() => {
    if (dateFilterOptions && dateFilterOptions.length > 0) {
      setSelectedDateFilter(dateFilterOptions[0].value)
    }
  }, [dateFilterOptions])

  return (
    <div className="flex flex-col">
      <SimpleHeader
        title={title}
        total={total}
        searchQuery={searchQuery}
        onSearch={onSearch}
        onAdd={onAdd}
        onBulk={onBulk}
        onToggleAdvanced={() => setShowAdvancedFilters(!showAdvancedFilters)}
        showAdvancedToggle={showAdvancedToggle}
        onSearchChange={handleSearch}
      />

      {showAdvancedFilters && (
        <AdvancedFilters
          dateFilterOptions={dateFilterOptions}
          filters={filters}
          sortOptions={sortOptions}
          selectedDateFilter={selectedDateFilter}
          selectedFilters={selectedFilters}
          selectedSortField={selectedSortField}
          selectedSortOrder={selectedSortOrder}
          showDateFilterDropdown={showDateFilterDropdown}
          showFilterDropdown={showFilterDropdown}
          onToggleDateFilter={toggleDateFilterDropdown}
          onToggleFilter={toggleFilterDropdown}
          onDateFilterChange={handleDateFilterChange}
          onFilterChange={handleFilters}
          onSortFieldChange={handleSortField}
          onSortOrderChange={handleSortOrder}
          onCetak={onCetak}
          cetakRef={cetakRef}
          title={title}
        />
      )}
    </div>
  )
}
