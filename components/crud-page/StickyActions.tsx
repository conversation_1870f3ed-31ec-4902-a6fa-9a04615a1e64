'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { Save } from 'lucide-react'
import { StickyActionsProps } from './data-editor-types'

export default function StickyActions({
  isEditMode,
  isSaving,
  config,
  onCancel,
  onSubmit
}: StickyActionsProps) {
  return (
    <div className="sticky bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
      <div className="container mx-auto p-4 max-w-4xl">
        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSaving}
          >
            {config.cancelButtonText || 'Cancel'}
          </Button>
          <Button 
            type="submit" 
            disabled={isSaving}
            onClick={onSubmit}
          >
            {isSaving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                {config.submitButtonText || (isEditMode ? 'Update' : 'Create')}
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}
