// File parsing utilities for DataBulkPage

import { SupportedFormat } from './types'

// Parse different file formats
export const parseFile = async (file: File, format: SupportedFormat, maxRecords?: number): Promise<Record<string, any>[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string
        let data: Record<string, any>[] = []
        
        switch (format) {
          case 'csv':
            data = parseCSV(content)
            break
          case 'json':
            data = JSON.parse(content)
            if (!Array.isArray(data)) {
              throw new Error('JSON file must contain an array of objects')
            }
            break
          case 'xlsx':
            // For now, we'll handle XLSX as CSV (would need a library like xlsx for full support)
            data = parseCSV(content)
            break
          case 'xml':
            data = parseXML(content)
            break
          default:
            throw new Error(`Unsupported format: ${format}`)
        }
        
        // Validate record count
        if (maxRecords && data.length > maxRecords) {
          throw new Error(`Too many records. Maximum allowed: ${maxRecords}`)
        }
        
        resolve(data)
      } catch (error) {
        reject(error)
      }
    }
    
    reader.onerror = () => reject(new Error('Failed to read file'))
    reader.readAsText(file)
  })
}

// Simple CSV parser
export const parseCSV = (content: string): Record<string, any>[] => {
  const lines = content.trim().split('\n')
  if (lines.length < 2) throw new Error('CSV file must have at least a header and one data row')
  
  const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
  const data: Record<string, any>[] = []
  
  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''))
    const row: Record<string, any> = {}
    
    headers.forEach((header, index) => {
      row[header] = values[index] || ''
    })
    
    data.push(row)
  }
  
  return data
}

// Simple XML parser (basic implementation)
export const parseXML = (content: string): Record<string, any>[] => {
  // This is a very basic XML parser - in production, use a proper XML parsing library
  const parser = new DOMParser()
  const xmlDoc = parser.parseFromString(content, 'text/xml')
  const items = xmlDoc.getElementsByTagName('item')
  
  const data: Record<string, any>[] = []
  for (let i = 0; i < items.length; i++) {
    const item = items[i]
    const row: Record<string, any> = {}
    
    for (let j = 0; j < item.children.length; j++) {
      const child = item.children[j]
      row[child.tagName] = child.textContent || ''
    }
    
    data.push(row)
  }
  
  return data
}

// Convert data to CSV
export const convertToCSV = (data: Record<string, any>[]): string => {
  if (data.length === 0) return ''
  
  const headers = Object.keys(data[0])
  const csvContent = [
    headers.join(','),
    ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
  ].join('\n')
  
  return csvContent
}

// Download file
export const downloadFile = (content: string, filename: string, mimeType: string) => {
  const blob = new Blob([content], { type: mimeType })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

// Validate file before processing
export const validateFile = (file: File, supportedFormats: SupportedFormat[], maxFileSize?: number): string | null => {
  // Validate file size
  if (maxFileSize && file.size > maxFileSize * 1024 * 1024) {
    return `File size must not exceed ${maxFileSize}MB`
  }

  // Validate file format
  const fileExtension = file.name.split('.').pop()?.toLowerCase() as SupportedFormat
  if (!supportedFormats.includes(fileExtension)) {
    return `Unsupported file format. Supported formats: ${supportedFormats.join(', ')}`
  }

  return null
}
