'use client'

import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Upload, Edit, Trash2 } from 'lucide-react'
import { useLocalization } from '@/hooks/useLocalization/client'
import { locales } from '../locales'
import { BulkOperationType, DataBulkConfig } from './types'

interface OperationTabsProps {
  config: DataBulkConfig
  activeOperation: BulkOperationType
  onOperationChange: (operation: BulkOperationType) => void
  children: React.ReactNode
}

export default function OperationTabs({ 
  config, 
  activeOperation, 
  onOperationChange,
  children 
}: OperationTabsProps) {
  const { t } = useLocalization('crud-page', locales)

  return (
    <Tabs 
      value={activeOperation} 
      onValueChange={(value) => onOperationChange(value as BulkOperationType)} 
      className="w-full"
    >
      <TabsList className="grid w-full grid-cols-3">
        {config.supportedOperations.includes('import') && (
          <TabsTrigger value="import" className="flex items-center gap-2">
            <Upload className="w-4 h-4" />
            {t('data_bulk_import')}
          </TabsTrigger>
        )}
        {config.supportedOperations.includes('update') && (
          <TabsTrigger value="update" className="flex items-center gap-2">
            <Edit className="w-4 h-4" />
            {t('data_bulk_update')}
          </TabsTrigger>
        )}
        {config.supportedOperations.includes('delete') && (
          <TabsTrigger value="delete" className="flex items-center gap-2">
            <Trash2 className="w-4 h-4" />
            {t('data_bulk_delete')}
          </TabsTrigger>
        )}
      </TabsList>
      
      {children}
    </Tabs>
  )
}
