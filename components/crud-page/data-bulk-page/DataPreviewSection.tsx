'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, XCircle, AlertTriangle } from 'lucide-react'
import { useLocalization } from '@/hooks/useLocalization/client'
import { locales } from '../locales'
import { TableComponent } from '../index'
import { ValidationResult, BulkOperationType } from './types'

interface DataPreviewSectionProps {
  uploadedData: Record<string, any>[]
  validationResults: ValidationResult[]
  activeOperation: BulkOperationType
  isProcessing: boolean
  onReset: () => void
  onExecute: () => void
}

export default function DataPreviewSection({
  uploadedData,
  validationResults,
  activeOperation,
  isProcessing,
  onReset,
  onExecute
}: DataPreviewSectionProps) {
  const { t } = useLocalization('crud-page', locales)

  if (uploadedData.length === 0) {
    return null
  }

  // Prepare data for table display
  const tableHeaders = Object.keys(uploadedData[0])
  const tableData = uploadedData.map((row, index) => ({
    id: index.toString(),
    columns: tableHeaders.map(header => row[header] || '')
  }))

  const validRecords = validationResults.filter(r => r.isValid).length
  const invalidRecords = validationResults.filter(r => !r.isValid).length

  const getOperationButtonText = () => {
    switch (activeOperation) {
      case 'import': return t('bulk_import_import_data')
      case 'update': return t('data_bulk_update_data')
      case 'delete': return t('data_bulk_delete_data')
      default: return t('execute_button')
    }
  }

  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>
            {t('bulk_import_data_preview')} ({uploadedData.length} {t('bulk_import_records')})
          </span>
          <div className="flex gap-2">
            <Button variant="outline" onClick={onReset} disabled={isProcessing}>
              {t('bulk_import_reset')}
            </Button>
            <Button onClick={onExecute} disabled={isProcessing || invalidRecords > 0}>
              {getOperationButtonText()}
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Validation Summary */}
        {validationResults.length > 0 && (
          <div className="mb-4 space-y-2">
            <h4 className="font-medium">{t('bulk_import_validation_summary')}</h4>
            <div className="flex gap-4">
              <Badge variant="default" className="bg-green-100 text-green-800">
                <CheckCircle className="w-3 h-3 mr-1" />
                {t('bulk_import_valid')}: {validRecords}
              </Badge>
              {invalidRecords > 0 && (
                <Badge variant="destructive">
                  <XCircle className="w-3 h-3 mr-1" />
                  {t('bulk_import_errors')}: {invalidRecords}
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Data Table */}
        <div className="border rounded-lg overflow-hidden">
          <TableComponent
            headers={tableHeaders}
            data={tableData.slice(0, 100)} // Show first 100 rows
            action={{ delete: false, edit: false }}
            isScroll={true}
            defaultColumnWidth="150px"
          />
          {uploadedData.length > 100 && (
            <div className="bg-gray-50 px-3 py-2 text-sm text-gray-600 text-center">
              {t('bulk_import_showing_first')} 100 {t('bulk_import_records_of')} {uploadedData.length} {t('bulk_import_total')}
            </div>
          )}
        </div>

        {/* Validation Errors */}
        {invalidRecords > 0 && (
          <div className="mt-4">
            <h4 className="font-medium mb-2">{t('bulk_import_validation_errors')}</h4>
            <div className="max-h-48 overflow-auto border rounded-lg">
              {validationResults
                .filter(r => !r.isValid)
                .slice(0, 50)
                .map((result, index) => (
                  <div key={index} className="p-3 border-b last:border-b-0 bg-red-50">
                    <div className="flex items-start gap-2">
                      <XCircle className="w-4 h-4 text-red-600 mt-0.5" />
                      <div>
                        <div className="font-medium text-red-900">
                          {t('bulk_import_row')} {result.rowIndex + 1}
                        </div>
                        {result.errors.map((error, errorIndex) => (
                          <div key={errorIndex} className="text-sm text-red-700">{error}</div>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              {invalidRecords > 50 && (
                <div className="p-3 text-center text-sm text-gray-600">
                  ... {t('bulk_import_and')} {invalidRecords - 50} {t('bulk_import_more_errors')}
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
