'use client'

import { But<PERSON> } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useLocalization } from '@/hooks/useLocalization/client'
import { locales } from '../locales'
import { DataBulkConfig } from './types'

interface HeaderSectionProps {
  config: DataBulkConfig
}

export default function HeaderSection({ config }: HeaderSectionProps) {
  const { t } = useLocalization('crud-page', locales)
  const router = useRouter()

  return (
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-4">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => router.push(config.backRoute)}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          {t('back_button')}
        </Button>
        <div>
          <h1 className="text-2xl font-bold">{config.title}</h1>
          {config.subtitle && (
            <p className="text-gray-600">{config.subtitle}</p>
          )}
        </div>
      </div>
    </div>
  )
}
