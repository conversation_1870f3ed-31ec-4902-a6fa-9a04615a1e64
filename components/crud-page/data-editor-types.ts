// Field types
export type FieldType = 
  | 'text' 
  | 'email' 
  | 'phone' 
  | 'number' 
  | 'textarea' 
  | 'select' 
  | 'multiselect'
  | 'image' 
  | 'file'
  | 'date'
  | 'datetime'
  | 'password'
  | 'url'

// Validation rules
export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  min?: number
  max?: number
  pattern?: RegExp
  custom?: (value: any) => string | null
}

// Field configuration
export interface FieldConfig {
  name: string
  label: string
  type: FieldType
  placeholder?: string
  description?: string
  validation?: ValidationRule
  options?: Array<{ value: string; label: string }>
  disabled?: boolean
  hidden?: boolean
  defaultValue?: any
  accept?: string // For file inputs
  multiple?: boolean // For file/multiselect inputs
  rows?: number // For textarea
  step?: number // For number inputs
  group?: string // For grouping fields
}

// Form section for grouping
export interface FormSection {
  title: string
  description?: string
  fields: string[] // Field names in this section
}

// Main configuration interface
export interface DataEditorConfig {
  title: string
  subtitle?: string
  fields: FieldConfig[]
  sections?: FormSection[]
  
  // Data operations
  fetchData?: (id: string) => Promise<Record<string, any>>
  saveData: (data: Record<string, any>, isEdit: boolean) => Promise<void>
  
  // Navigation
  backRoute: string
  successRoute?: string
  
  // Customization
  submitButtonText?: string
  cancelButtonText?: string
  showImagePreview?: boolean
  maxFileSize?: number // in MB
  allowedFileTypes?: string[]
}

// Component props interfaces
export interface DataEditorPageProps {
  config: DataEditorConfig
  id?: string // If provided, it's edit mode
}

export interface FieldRendererProps {
  field: FieldConfig
  value: any
  error?: string
  onChange: (fieldName: string, value: any) => void
  onFileUpload: (fieldName: string, files: FileList | null) => void
  imagePreview?: Record<string, string>
  setImagePreview?: React.Dispatch<React.SetStateAction<Record<string, string>>>
  config: DataEditorConfig
}

export interface LoadingStateProps {
  config: DataEditorConfig
  isEditMode: boolean
  onCancel: () => void
}

export interface ErrorStateProps {
  config: DataEditorConfig
  isEditMode: boolean
  error: string
  onCancel: () => void
  onRetry: () => void
}

export interface StickyActionsProps {
  isEditMode: boolean
  isSaving: boolean
  config: DataEditorConfig
  onCancel: () => void
  onSubmit: (e: React.FormEvent) => void
}
