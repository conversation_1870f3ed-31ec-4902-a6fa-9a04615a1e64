{"page_title": "Data Management", "page_subtitle": "Manage and monitor your data easily", "stats_tab": "Statistics & Summary", "table_tab": "Data Table", "dashboard_title": "Statistics Dashboard", "dashboard_subtitle": "Data summary and performance analysis", "total_label": "Total", "items_label": "items", "data_table_title": "Data Management", "pinned_columns": "Pinned Columns", "pinned_columns_count": "columns pinned", "pinned_columns_help": "Click column names to pin/unpin them. Pinned columns stay visible when scrolling horizontally.", "of": "of", "pin_icon": "📌", "unpin_icon": "📍", "loading_message": "Loading data...", "no_data_message": "No data available", "add_button": "Add", "edit_button": "Edit", "delete_button": "Delete", "print_button": "Print", "search_placeholder": "Search", "filter_placeholder": "Select Filter", "sort_placeholder": "Select Column", "date_filter_placeholder": "Select Time Range", "custom_date_start": "Start Date", "custom_date_end": "End Date", "success_title": "Success", "error_title": "Error", "delete_success": "Data has been deleted successfully", "delete_error": "An error occurred while deleting data", "load_error": "An error occurred while loading data", "today": "Today", "yesterday": "Yesterday", "this_week": "This Week", "last_week": "Last Week", "this_month": "This Month", "last_month": "Last Month", "this_year": "This Year", "last_year": "Last Year", "custom_range": "Custom Range", "all_data": "All Data", "today_desc": "Today's data", "yesterday_desc": "Yesterday's data", "this_week_desc": "Current week", "last_week_desc": "Previous week", "this_month_desc": "Current month", "last_month_desc": "Previous month", "this_year_desc": "Current year", "last_year_desc": "Previous year", "custom_range_desc": "Select custom date range", "all_data_desc": "All time periods", "asc": "ASC", "desc": "DESC", "table_no_column": "No", "table_action_column": "Action", "table_no_data": "No data available", "pagination_page": "Page", "pagination_of": "of", "bulk_import_title": "Import Data", "bulk_import_choose_file": "Choose <PERSON>", "bulk_import_supported_formats": "Supported formats", "bulk_import_max_file_size": "Maximum file size", "bulk_import_need_template": "Need a template?", "bulk_import_template_description": "Download a template file with the correct format", "bulk_import_download_template": "Download Template", "bulk_import_processing": "Processing...", "bulk_import_data_preview": "Data Preview", "bulk_import_records": "records", "bulk_import_reset": "Reset", "bulk_import_import_data": "Import Data", "bulk_import_validation_summary": "Validation Summary", "bulk_import_valid": "<PERSON><PERSON>", "bulk_import_errors": "Errors", "bulk_import_showing_first": "Showing first", "bulk_import_records_of": "records of", "bulk_import_total": "total", "bulk_import_validation_errors": "Validation Errors", "bulk_import_row": "Row", "bulk_import_field": "Field", "bulk_import_and": "and", "bulk_import_more_errors": "more errors", "bulk_import_results": "Import Results", "bulk_import_successful": "Successful", "bulk_import_failed": "Failed", "bulk_import_import_errors": "Import Errors", "bulk_delete_title": "Bulk Delete", "bulk_delete_warning": "Warning", "bulk_delete_warning_description": "This action will permanently delete the selected", "bulk_delete_cannot_undo": "This cannot be undone. Please review your selection carefully.", "bulk_delete_select_items": "Select", "bulk_delete_selected": "selected", "bulk_delete_select_all": "Select All", "bulk_delete_select": "Select", "bulk_delete_selected_for_deletion": "selected for deletion", "bulk_delete_delete_selected": "Delete Selected", "bulk_delete_confirm_title": "Confirm Bulk Delete", "bulk_delete_confirm_description": "You are about to permanently delete", "bulk_delete_selected_items": "Selected", "bulk_delete_cannot_undo_confirm": "This action cannot be undone. Are you sure you want to continue?", "bulk_delete_cancel": "Cancel", "bulk_delete_deleting": "Deleting...", "bulk_delete_results": "Delete Results", "bulk_delete_deleted": "Deleted", "bulk_delete_errors": "Delete Errors", "bulk_update_workflow_title": "Bulk Update Workflow", "bulk_update_how_to": "How to bulk update", "bulk_update_step_1": "Select the records you want to update from the table below", "bulk_update_step_2": "Click \"Export Selected\" to download a CSV file with the selected data", "bulk_update_step_3": "Modify the CSV file with your changes (keep the ID column unchanged)", "bulk_update_step_4": "Click \"Import Updates\" and upload your modified CSV file", "bulk_update_step_5": "Review the preview and click \"Import Data\" to apply the updates", "bulk_update_import_title": "Import Updated Data", "bulk_update_back_to_selection": "Back to Selection", "bulk_update_import_description": "Upload the CSV file you exported and modified. The system will update the selected records with the new data.", "bulk_update_data_selection": "Data Selection", "bulk_update_export_selected": "Export Selected", "bulk_update_import_updates": "Import Updates", "bulk_update_selected_for_update": "selected for bulk update", "data_bulk_import": "Import", "data_bulk_update": "Update", "data_bulk_delete": "Delete", "data_bulk_import_data": "Import Data", "data_bulk_update_data": "Update Data", "data_bulk_delete_data": "Delete Data", "data_bulk_update_requirements": "Update Requirements", "data_bulk_update_id_required": "Each record must include an ID field to identify which record to update.", "data_bulk_update_id_optional": "Records can be updated based on matching criteria or ID fields.", "data_bulk_delete_id_required": "Each record must include an ID field to identify which record to delete.", "data_bulk_operation_results": "Operation Results", "data_bulk_error_details": "<PERSON><PERSON><PERSON>", "execute_button": "Execute", "back_button": "Back", "bulk_ops_button": "Bulk Ops", "more_button": "More", "select_filter": "Select Filter", "select_date_range": "Select Date Range", "select_column": "Select Column", "asc_order": "ASC", "desc_order": "DESC", "print_not_available": "Print not available yet", "table_view": "Table View", "statistics": "Statistics", "trends": "Trends", "trends_coming_soon": "Trends Coming Soon", "trends_description": "Advanced trend analysis and insights will be available here soon."}