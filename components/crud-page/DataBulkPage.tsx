'use client'

import { useState } from 'react'
import { toast } from '@/hooks/use-toast'
import { useLocalization } from '@/hooks/useLocalization/client'
import { locales } from './locales'

// Import components
import HeaderSection from './data-bulk-page/HeaderSection'
import OperationTabs from './data-bulk-page/OperationTabs'
import ImportTabContent from './data-bulk-page/ImportTabContent'
import UpdateTabContent from './data-bulk-page/UpdateTabContent'
import DeleteTabContent from './data-bulk-page/DeleteTabContent'
import DataPreviewSection from './data-bulk-page/DataPreviewSection'
import OperationResultsSection from './data-bulk-page/OperationResultsSection'
import ProgressSection from './bulk-import/ProgressSection'

// Import types and utilities
import {
  DataBulkConfig,
  BulkOperationType,
  ValidationResult,
  BulkOperationResult,
  SupportedFormat
} from './data-bulk-page/types'
import { parseFile, validateFile } from './data-bulk-page/fileUtils'

// Re-export types for backward compatibility
export type {
  DataBulkConfig,
  BulkOperationType,
  ValidationResult,
  BulkOperationResult,
  SupportedFormat
} from './data-bulk-page/types'

interface DataBulkPageProps {
  config: DataBulkConfig
}

export default function DataBulkPage({ config }: DataBulkPageProps) {
  const { t } = useLocalization("crud-page", locales)

  // State management
  const [activeOperation, setActiveOperation] = useState<BulkOperationType>('import')
  const [uploadedData, setUploadedData] = useState<Record<string, any>[]>([])
  const [validationResults, setValidationResults] = useState<ValidationResult[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [progress, setProgress] = useState(0)
  const [operationResult, setOperationResult] = useState<BulkOperationResult | null>(null)

  // Handle file upload
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file
    const validationError = validateFile(file, config.supportedFormats, config.maxFileSize)
    if (validationError) {
      toast({
        title: t('error_title'),
        description: validationError,
        variant: 'destructive'
      })
      return
    }

    const fileExtension = file.name.split('.').pop()?.toLowerCase() as SupportedFormat
    setIsProcessing(true)
    setProgress(10)

    try {
      const data = await parseFile(file, fileExtension, config.maxRecords)
      setProgress(50)

      // Validate data if validation function is provided
      if (config.validateData) {
        const validationResults = config.validateData(data)
        setValidationResults(validationResults)
        setProgress(80)
      }

      setUploadedData(data)
      setProgress(100)

      toast({
        title: t('success_title'),
        description: `File uploaded successfully. ${data.length} records found.`,
        variant: 'default'
      })
    } catch (error) {
      console.error('File parsing error:', error)
      toast({
        title: t('error_title'),
        description: 'Failed to parse file. Please check the file format.',
        variant: 'destructive'
      })
    } finally {
      setIsProcessing(false)
      setProgress(0)
    }
  }



  // Execute bulk operation
  const executeBulkOperation = async () => {
    if (uploadedData.length === 0) {
      toast({
        title: t('error_title'),
        description: 'No data to process',
        variant: 'destructive'
      })
      return
    }

    setIsProcessing(true)
    setProgress(0)
    setOperationResult(null)

    try {
      let result: BulkOperationResult

      switch (activeOperation) {
        case 'import':
          if (!config.bulkImport) throw new Error('Bulk import not supported')
          result = await config.bulkImport(uploadedData)
          break
        case 'update':
          if (!config.bulkUpdate) throw new Error('Bulk update not supported')
          result = await config.bulkUpdate(uploadedData)
          break
        case 'delete':
          if (!config.bulkDelete) throw new Error('Bulk delete not supported')
          const ids = uploadedData.map(item => item.id).filter(Boolean)
          result = await config.bulkDelete(ids)
          break
        default:
          throw new Error(`Unsupported operation: ${activeOperation}`)
      }

      setOperationResult(result)
      setProgress(100)

      toast({
        title: t('success_title'),
        description: `Operation completed. ${result.successful}/${result.total} records processed successfully.`,
        variant: result.failed > 0 ? 'destructive' : 'default'
      })
    } catch (error) {
      console.error('Bulk operation error:', error)
      toast({
        title: t('error_title'),
        description: error instanceof Error ? error.message : 'Bulk operation failed',
        variant: 'destructive'
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // Reset state
  const resetState = () => {
    setUploadedData([])
    setValidationResults([])
    setOperationResult(null)
    setProgress(0)
  }

  // Handle operation change
  const handleOperationChange = (operation: BulkOperationType) => {
    setActiveOperation(operation)
    resetState()
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <HeaderSection config={config} />

      <OperationTabs
        config={config}
        activeOperation={activeOperation}
        onOperationChange={handleOperationChange}
      >
        <ImportTabContent
          config={config}
          onFileUpload={handleFileUpload}
          isProcessing={isProcessing}
        />

        <UpdateTabContent
          config={config}
          onFileUpload={handleFileUpload}
          isProcessing={isProcessing}
        />

        <DeleteTabContent
          config={config}
          onFileUpload={handleFileUpload}
          isProcessing={isProcessing}
        />
      </OperationTabs>

      <ProgressSection
        isProcessing={isProcessing}
        progress={progress}
      />

      <DataPreviewSection
        uploadedData={uploadedData}
        validationResults={validationResults}
        activeOperation={activeOperation}
        isProcessing={isProcessing}
        onReset={resetState}
        onExecute={executeBulkOperation}
      />

      <OperationResultsSection
        operationResult={operationResult}
      />
    </div>
  )
}

// Export types for external use
// export type { DataBulkConfig, SupportedFormat, BulkOperationType, ValidationResult, BulkOperationResult, FieldMapping }
