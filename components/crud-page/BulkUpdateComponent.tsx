'use client'

import { useState, useEffect } from 'react'
import { toast } from '@/hooks/use-toast'
import { useLocalization } from '@/hooks/useLocalization/client'
import { locales } from './locales'

// Import components
import UpdateInstructionsSection from './bulk-update/UpdateInstructionsSection'
import UpdateSelectionSection from './bulk-update/UpdateSelectionSection'
import ImportModeSection from './bulk-update/ImportModeSection'

// Import types and utilities
import { BulkUpdateConfig } from './bulk-update/types'
import { convertToCSV, downloadFile } from './bulk-update/csvUtils'
import type { BulkImportConfig } from './bulk-import/FileParsingUtils'

// Re-export types for backward compatibility
export type { BulkUpdateConfig } from './bulk-update/types'

interface BulkUpdateComponentProps {
  config: BulkUpdateConfig
  onSuccess?: () => void
}

export default function BulkUpdateComponent({ config, onSuccess }: BulkUpdateComponentProps) {
  const { t } = useLocalization('crud-page', locales)

  // State
  const [data, setData] = useState<Record<string, any>[]>([])
  const [selectedIds, setSelectedIds] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showImport, setShowImport] = useState(false)
  const [exportedData, setExportedData] = useState<Record<string, any>[]>([])

  // Load data
  const loadData = async () => {
    setIsLoading(true)
    try {
      const fetchedData = await config.fetchData()
      setData(fetchedData)
    } catch (error) {
      console.error('Error loading data:', error)
      toast({
        title: t('error_title'),
        description: t('load_error'),
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Handle row selection
  const handleRowSelect = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedIds(prev => [...prev, id])
    } else {
      setSelectedIds(prev => prev.filter(selectedId => selectedId !== id))
    }
  }

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedIds(data.map(item => item.id))
    } else {
      setSelectedIds([])
    }
  }

  // Export selected data to CSV
  const exportSelectedData = () => {
    if (selectedIds.length === 0) {
      toast({
        title: t('error_title'),
        description: 'Please select at least one record to export',
        variant: 'destructive'
      })
      return
    }

    const selectedData = data.filter(item => selectedIds.includes(item.id))
    setExportedData(selectedData)

    const csv = convertToCSV(selectedData)
    downloadFile(csv, 'bulk_update_data.csv', 'text/csv')

    toast({
      title: t('success_title'),
      description: `Exported ${selectedData.length} records to CSV`,
      variant: 'default'
    })
  }

  // Show import interface
  const showImportInterface = () => {
    if (selectedIds.length === 0) {
      toast({
        title: t('error_title'),
        description: 'Please select records first, then export and modify the CSV file',
        variant: 'destructive'
      })
      return
    }
    setShowImport(true)
  }

  // Handle successful import (update)
  const handleImportSuccess = () => {
    setShowImport(false)
    setSelectedIds([])
    loadData() // Refresh data
    if (onSuccess) {
      onSuccess()
    }
  }

  // Load data on component mount
  useEffect(() => {
    loadData()
  }, [])

  // Enhanced import config for updates
  const updateImportConfig: BulkImportConfig = {
    ...config.importConfig,
    importData: async (importData: Record<string, any>[]) => {
      return await config.updateData(importData)
    }
  }

  if (showImport) {
    return (
      <ImportModeSection
        importConfig={updateImportConfig}
        onBack={() => setShowImport(false)}
        onSuccess={handleImportSuccess}
      />
    )
  }

  return (
    <div className="space-y-6">
      <UpdateInstructionsSection />

      <UpdateSelectionSection
        config={config}
        data={data}
        selectedIds={selectedIds}
        isLoading={isLoading}
        onRowSelect={handleRowSelect}
        onSelectAll={handleSelectAll}
        onExport={exportSelectedData}
        onImport={showImportInterface}
      />
    </div>
  )
}
