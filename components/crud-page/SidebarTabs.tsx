'use client'

import { useState } from 'react'
import { Table, BarChart3, Pin, TrendingUp } from 'lucide-react'
import { useLocalization } from '@/hooks/useLocalization/client'
import { locales } from './locales'

interface SidebarTabsProps {
  activeTab: string
  onTabChange: (tab: string) => void
  children: React.ReactNode
}

interface TabConfig {
  id: string
  icon: React.ComponentType<{ className?: string }>
  labelKey?: string
  label?: string
}

const tabConfigs: TabConfig[] = [
  { id: 'table', icon: Table, labelKey: 'table_view' },
  { id: 'stats', icon: BarChart3, labelKey: 'statistics' },
  { id: 'columns', icon: Pin, label: 'Columns' },
  { id: 'trends', icon: TrendingUp, labelKey: 'trends' }
]

export default function   SidebarTabs({ activeTab, onTabChange, children }: SidebarTabsProps) {
  const { t } = useLocalization('crud-page', locales)
  const [hoveredTab, setHoveredTab] = useState<string | null>(null)

  return (
    <div className="flex h-full">
      {/* Sidebar */}
      <div className="w-[100px] bg-gray-50 border-r border-gray-200 flex flex-col">
        {tabConfigs.map((tab) => {
          const Icon = tab.icon
          const isActive = activeTab === tab.id
          const isHovered = hoveredTab === tab.id
          const label = tab.label || (tab.labelKey ? t(tab.labelKey) : tab.id)

          return (
            <div
              key={tab.id}
              className="relative"
              onMouseEnter={() => setHoveredTab(tab.id)}
              onMouseLeave={() => setHoveredTab(null)}
            >
              <button
                onClick={() => onTabChange(tab.id)}
                className={`
                  w-full h-16 flex flex-col items-center justify-center gap-1 
                  transition-all duration-200 relative
                  ${isActive 
                    ? 'bg-blue-100 text-blue-600 border-r-2 border-blue-600' 
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  }
                `}
              >
                <Icon className="w-5 h-5" />
                <span className="text-xs font-medium truncate px-1">
                  {tab.id === 'table' ? 'Table' : 
                   tab.id === 'stats' ? 'Stats' : 
                   tab.id === 'columns' ? 'Cols' : 
                   'Trend'}
                </span>
              </button>

              {/* Tooltip on hover */}
              {isHovered && (
                <div className="absolute left-full top-1/2 transform -translate-y-1/2 ml-2 z-50">
                  <div className="bg-gray-900 text-white text-sm px-2 py-1 rounded shadow-lg whitespace-nowrap">
                    {label}
                    <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-0 h-0 border-t-4 border-b-4 border-r-4 border-transparent border-r-gray-900"></div>
                  </div>
                </div>
              )}
            </div>
          )
        })}
      </div>

      {/* Content Area */}
      <div className="flex-1 overflow-hidden">
        {children}
      </div>
    </div>
  )
}

// Individual tab content components for better organization
interface TabContentProps {
  value: string
  activeTab: string
  children: React.ReactNode
  className?: string
}

export function SidebarTabContent({ value, activeTab, children, className = '' }: TabContentProps) {
  if (value !== activeTab) return null
  
  return (
    <div className={`h-full overflow-auto ${className}`}>
      {children}
    </div>
  )
}
