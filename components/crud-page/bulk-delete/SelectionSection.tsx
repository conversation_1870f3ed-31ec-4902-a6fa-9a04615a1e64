'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { AlertTriangle } from 'lucide-react'
import { useLocalization } from '@/hooks/useLocalization/client'
import { locales } from '../locales'
import { BulkDeleteConfig } from './types'

interface SelectionSectionProps {
  config: BulkDeleteConfig
  data: Record<string, any>[]
  selectedIds: string[]
  isLoading: boolean
  onRowSelect: (id: string, checked: boolean) => void
  onSelectAll: (checked: boolean) => void
  children?: React.ReactNode // For delete button
}

export default function SelectionSection({
  config,
  data,
  selectedIds,
  isLoading,
  onRowSelect,
  onSelectAll,
  children
}: SelectionSectionProps) {
  const { t } = useLocalization('crud-page', locales)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>{t('bulk_delete_select_items')} {config.itemNamePlural}</span>
          <div className="flex items-center gap-2">
            <Badge variant="secondary">
              {selectedIds.length} {t('pagination_of')} {data.length} {t('bulk_delete_selected')}
            </Badge>
            {children}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Select All Checkbox */}
        <div className="flex items-center space-x-2 mb-4">
          <Checkbox
            id="select-all"
            checked={selectedIds.length === data.length && data.length > 0}
            onCheckedChange={onSelectAll}
          />
          <label htmlFor="select-all" className="text-sm font-medium">
            {t('bulk_delete_select_all')} ({data.length} {data.length === 1 ? config.itemName : config.itemNamePlural})
          </label>
        </div>

        {/* Data Table with Selection */}
        <div className="border rounded-lg overflow-hidden">
          <table className="w-full text-sm">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-3 py-2 text-left font-medium text-gray-900 w-12">
                  {t('bulk_delete_select')}
                </th>
                {config.headers.map((header) => (
                  <th key={header} className="px-3 py-2 text-left font-medium text-gray-900">
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {isLoading ? (
                <tr>
                  <td colSpan={config.headers.length + 1} className="px-3 py-8 text-center text-gray-500">
                    {t('loading_message')}
                  </td>
                </tr>
              ) : data.length === 0 ? (
                <tr>
                  <td colSpan={config.headers.length + 1} className="px-3 py-8 text-center text-gray-500">
                    {t('no_data_message')}
                  </td>
                </tr>
              ) : (
                data.map((item) => {
                  const row = config.transformToTableRow(item)
                  const isSelected = selectedIds.includes(item.id)
                  return (
                    <tr key={item.id} className={`border-t ${isSelected ? 'bg-red-50' : ''}`}>
                      <td className="px-3 py-2">
                        <Checkbox
                          checked={isSelected}
                          onCheckedChange={(checked) => onRowSelect(item.id, checked as boolean)}
                        />
                      </td>
                      {row.columns.map((cell, cellIndex) => (
                        <td key={cellIndex} className="px-3 py-2">
                          {cell}
                        </td>
                      ))}
                    </tr>
                  )
                })
              )}
            </tbody>
          </table>
        </div>

        {/* Selection Info */}
        {selectedIds.length > 0 && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-4 h-4 text-red-600" />
              <span className="text-sm text-red-800">
                {selectedIds.length} {selectedIds.length === 1 ? config.itemName : config.itemNamePlural} {t('bulk_delete_selected_for_deletion')}
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
