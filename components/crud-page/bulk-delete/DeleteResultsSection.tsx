'use client'

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { CheckCircle, XCircle } from 'lucide-react'
import { useLocalization } from '@/hooks/useLocalization/client'
import { locales } from '../locales'
import { DeleteResult } from './types'

interface DeleteResultsSectionProps {
  deleteResult: DeleteResult | null
}

export default function DeleteResultsSection({ deleteResult }: DeleteResultsSectionProps) {
  const { t } = useLocalization('crud-page', locales)

  if (!deleteResult) {
    return null
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {deleteResult.failed === 0 ? (
            <CheckCircle className="w-5 h-5 text-green-600" />
          ) : (
            <XCircle className="w-5 h-5 text-red-600" />
          )}
          {t('bulk_delete_results')}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{deleteResult.total}</div>
            <div className="text-sm text-gray-600">{t('bulk_import_total')}</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{deleteResult.successful}</div>
            <div className="text-sm text-gray-600">{t('bulk_delete_deleted')}</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{deleteResult.failed}</div>
            <div className="text-sm text-gray-600">{t('bulk_import_failed')}</div>
          </div>
        </div>

        {/* Error Details */}
        {deleteResult.errors.length > 0 && (
          <div className="mt-4">
            <h4 className="font-medium mb-2">{t('bulk_delete_errors')}</h4>
            <div className="max-h-48 overflow-auto border rounded-lg">
              {deleteResult.errors.map((error, index) => (
                <div key={index} className="p-3 border-b last:border-b-0 bg-red-50">
                  <div className="flex items-start gap-2">
                    <XCircle className="w-4 h-4 text-red-600 mt-0.5" />
                    <div>
                      <div className="font-medium text-red-900">ID: {error.id}</div>
                      <div className="text-sm text-red-700">{error.message}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
