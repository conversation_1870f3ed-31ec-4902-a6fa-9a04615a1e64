'use client'

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Trash2, AlertTriangle } from 'lucide-react'
import { useLocalization } from '@/hooks/useLocalization/client'
import { locales } from '../locales'
import { BulkDeleteConfig } from './types'

interface InstructionsSectionProps {
  config: BulkDeleteConfig
}

export default function InstructionsSection({ config }: InstructionsSectionProps) {
  const { t } = useLocalization('crud-page', locales)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Trash2 className="w-5 h-5" />
          {t('bulk_delete_title')} {config.itemNamePlural}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start gap-2">
            <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-red-900">{t('bulk_delete_warning')}</h4>
              <p className="text-sm text-red-700 mt-1">
                {t('bulk_delete_warning_description')} {config.itemNamePlural}. {t('bulk_delete_cannot_undo')}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
