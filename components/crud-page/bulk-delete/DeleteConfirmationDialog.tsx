'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Trash2, AlertTriangle } from 'lucide-react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { useLocalization } from '@/hooks/useLocalization/client'
import { locales } from '../locales'
import { BulkDeleteConfig } from './types'

interface DeleteConfirmationDialogProps {
  config: BulkDeleteConfig
  selectedIds: string[]
  selectedItems: Record<string, any>[]
  isDeleting: boolean
  onConfirm: () => void
}

export default function DeleteConfirmationDialog({
  config,
  selectedIds,
  selectedItems,
  isDeleting,
  onConfirm
}: DeleteConfirmationDialogProps) {
  const { t } = useLocalization('crud-page', locales)

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button 
          variant="destructive"
          disabled={selectedIds.length === 0 || isDeleting}
        >
          <Trash2 className="w-4 h-4 mr-2" />
          {t('bulk_delete_delete_selected')} ({selectedIds.length})
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            {t('bulk_delete_confirm_title')}
          </AlertDialogTitle>
          <AlertDialogDescription>
            <div className="space-y-3">
              <p>
                {t('bulk_delete_confirm_description')} <strong>{selectedIds.length}</strong> {' '}
                {selectedIds.length === 1 ? config.itemName : config.itemNamePlural}.
              </p>
              
              {selectedItems.length > 0 && (
                <div>
                  <p className="font-medium mb-2">{t('bulk_delete_selected_items')} {config.itemNamePlural}:</p>
                  <div className="max-h-32 overflow-y-auto bg-gray-50 rounded p-2">
                    {selectedItems.slice(0, 10).map((item) => {
                      const row = config.transformToTableRow(item)
                      return (
                        <div key={item.id} className="text-sm py-1">
                          {row.columns[0]} {/* Show first column (usually name) */}
                        </div>
                      )
                    })}
                    {selectedItems.length > 10 && (
                      <div className="text-sm text-gray-500 py-1">
                        ... {t('bulk_import_and')} {selectedItems.length - 10} {t('bulk_import_more_errors')}
                      </div>
                    )}
                  </div>
                </div>
              )}
              
              <p className="text-red-600 font-medium">
                {t('bulk_delete_cannot_undo_confirm')}
              </p>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>{t('bulk_delete_cancel')}</AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            className="bg-red-600 hover:bg-red-700"
            disabled={isDeleting}
          >
            {isDeleting 
              ? t('bulk_delete_deleting') 
              : `${t('delete_button')} ${selectedIds.length} ${selectedIds.length === 1 ? config.itemName : config.itemNamePlural}`
            }
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
