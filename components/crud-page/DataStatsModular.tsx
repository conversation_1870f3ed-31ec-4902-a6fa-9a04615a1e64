'use client'

import { useState } from 'react'
import { ChevronDown, Filter, Users } from 'lucide-react'

// Import modular components
import { DataStatsProps, TimePeriod, StatCard, TimePeriodOption } from './stats/types'
import { getFilteredData, calculateStats } from './stats/calculations'
import { generateStatCards } from './stats/cardGenerators'

// Default time period options
const defaultTimePeriodOptions: TimePeriodOption[] = [
  { value: 'today', label: 'Today', description: 'Items from today' },
  { value: 'yesterday', label: 'Yesterday', description: 'Items from yesterday' },
  { value: 'this_week', label: 'This Week', description: 'Items from this week' },
  { value: 'last_week', label: 'Last Week', description: 'Items from last week' },
  { value: 'this_month', label: 'This Month', description: 'Items from this month' },
  { value: 'last_month', label: 'Last Month', description: 'Items from last month' },
  { value: 'this_year', label: 'This Year', description: 'Items from this year' },
  { value: 'last_year', label: 'Last Year', description: 'Items from last year' },
  { value: 'custom', label: 'Custom Range', description: 'Select custom date range' },
  { value: 'all', label: 'All Time', description: 'All available data' }
]

// Stat card component
const StatCardComponent: React.FC<{ card: StatCard }> = ({ card }) => (
  <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
    <div className="flex items-start justify-between">
      <div className="flex-1">
        <div className={`${card.bgColor} ${card.color} p-2 rounded-lg w-fit mb-2`}>
          {card.icon}
        </div>
        <p className="text-sm font-medium text-gray-600 mb-1">{card.title}</p>
        <p className="text-2xl font-bold text-gray-900 mb-1">{card.value}</p>
        {card.subtitle && (
          <p className="text-sm text-gray-500">{card.subtitle}</p>
        )}
        {card.trend && (
          <p className={`text-sm mt-1 ${card.trend.isPositive ? 'text-green-600' : 'text-red-600'}`}>
            {card.trend.value}
          </p>
        )}
      </div>
    </div>
  </div>
)

// Main DataStats component
export default function DataStatsModular({ 
  data, 
  config,
  isLoading = false
}: DataStatsProps) {
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('all')
  const [customStartDate, setCustomStartDate] = useState('')
  const [customEndDate, setCustomEndDate] = useState('')

  const timePeriodOptions = config.timePeriodOptions || defaultTimePeriodOptions

  // ✨ Shimmer Loading Components (same as before)
  const ShimmerHeader = () => (
    <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <div className="animate-pulse bg-gray-200 h-6 w-48 rounded mb-2"></div>
        <div className="animate-pulse bg-gray-200 h-4 w-32 rounded"></div>
      </div>
      <div className="flex flex-col sm:flex-row gap-3">
        <div className="animate-pulse bg-gray-200 h-10 w-48 rounded-lg"></div>
      </div>
    </div>
  )

  const ShimmerStatCard = () => (
    <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="animate-pulse bg-gray-200 w-6 h-6 rounded mb-2"></div>
          <div className="animate-pulse bg-gray-200 h-4 w-20 rounded mb-1"></div>
          <div className="animate-pulse bg-gray-200 h-8 w-16 rounded mb-1"></div>
          <div className="animate-pulse bg-gray-200 h-3 w-24 rounded"></div>
        </div>
      </div>
    </div>
  )

  const ShimmerMainStats = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {Array.from({ length: 8 }, (_, index) => (
        <ShimmerStatCard key={`main-shimmer-${index}`} />
      ))}
    </div>
  )

  const ShimmerAdditionalCard = () => (
    <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
      <div className="flex items-center mb-3">
        <div className="animate-pulse bg-gray-200 w-9 h-9 rounded-lg mr-3"></div>
        <div className="animate-pulse bg-gray-200 h-5 w-32 rounded"></div>
      </div>
      <div className="animate-pulse bg-gray-200 h-6 w-24 rounded mb-1"></div>
      <div className="animate-pulse bg-gray-200 h-4 w-40 rounded"></div>
    </div>
  )

  const ShimmerAdditionalInfo = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <ShimmerAdditionalCard />
      <ShimmerAdditionalCard />
    </div>
  )

  // ✨ Show shimmer loading state
  if (isLoading) {
    return (
      <div className="mb-6">
        <ShimmerHeader />
        <ShimmerMainStats />
        <ShimmerAdditionalInfo />
      </div>
    )
  }

  // Filter data based on selected period
  const filteredData = getFilteredData(data, selectedPeriod, customStartDate, customEndDate)
  
  // Calculate stats
  const stats = calculateStats(filteredData, config.calculations)
  
  // Generate stat cards
  const statCards = generateStatCards(stats, config.calculations, config.customStatCards)

  return (
    <div className="mb-6">
      {/* Header */}
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">{config.title || 'Statistics'}</h2>
          <p className="text-sm text-gray-600">
            Showing {filteredData.length} of {data.length} items
            {selectedPeriod !== 'all' && ` for ${timePeriodOptions.find(opt => opt.value === selectedPeriod)?.label.toLowerCase()}`}
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3">
          {/* Time Period Selector */}
          <div className="relative">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value as TimePeriod)}
              className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {timePeriodOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
          </div>

          {/* Custom Date Range */}
          {selectedPeriod === 'custom' && (
            <div className="flex gap-2">
              <input
                type="date"
                value={customStartDate}
                onChange={(e) => setCustomStartDate(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Start date"
              />
              <input
                type="date"
                value={customEndDate}
                onChange={(e) => setCustomEndDate(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="End date"
              />
            </div>
          )}
        </div>
      </div>

      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {statCards.map((card, index) => (
          <StatCardComponent key={`stat-card-${index}`} card={card} />
        ))}
      </div>

      {/* Additional Info Cards */}
      {(stats.topCategory || stats.topAssignee) && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {stats.topCategory && (
            <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
              <div className="flex items-center mb-3">
                <div className="bg-indigo-100 text-indigo-600 p-2 rounded-lg mr-3">
                  <Filter className="w-5 h-5" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">
                  Top {config.calculations.categoryLabel || 'Category'}
                </h3>
              </div>
              <p className="text-2xl font-bold text-gray-900">{stats.topCategory.name}</p>
              <p className="text-sm text-gray-600">{stats.topCategory.count} items</p>
            </div>
          )}

          {stats.topAssignee && (
            <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
              <div className="flex items-center mb-3">
                <div className="bg-cyan-100 text-cyan-600 p-2 rounded-lg mr-3">
                  <Users className="w-5 h-5" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">
                  Most Active {config.calculations.assignedToLabel || 'Assignee'}
                </h3>
              </div>
              <p className="text-2xl font-bold text-gray-900">{stats.topAssignee.name}</p>
              <p className="text-sm text-gray-600">{stats.topAssignee.count} items</p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
