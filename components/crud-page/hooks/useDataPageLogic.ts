'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { toast } from '@/hooks/use-toast'
import { TableRowData } from '../types'

export interface DataPageEnhancedConfig<T = any> {
  title: string
  subtitle?: string
  headers: string[]
  fetchData: (params: {
    search?: string;
    includeDeleted?: boolean;
    page?: number;
    limit?: number;
    sorts?: Array<{ field: string; direction: 'asc' | 'desc' }>;
    filters?: Array<{ field: string; value: any }>;
  }) => Promise<{
    items: T[]
    total: number
    totalPages: number
  }>
  fetchStats?: () => Promise<any>
  deleteItem?:  (id: string) => Promise<void>
  transformToTableRow: (item: T) => TableRowData
  addRoute: string
  editRoute: (id: string) => string
  bulkRoute?: string
  sortOptions?: Array<{ value: string; label: string }>
  dateFilterOptions?: Array<{ value: string; label: string }>
  filters?: Array<{ id: string; name: string }>
  columnWidths?: Record<string, string>
  defaultColumnWidth?: string
  defaultPageSize?: number
  pinnedColumns?: string[]
}

export function useDataPageLogic<T>(config: DataPageEnhancedConfig<T>) {
  const router = useRouter()

  // State management
  const [listData, setListData] = useState<TableRowData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedFilters, setSelectedFilters] = useState<string[]>([])
  const [selectedDateFilter, setSelectedDateFilter] = useState<string | null>(null)
  const [sortField, setSortField] = useState<string>('')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(config.defaultPageSize || 10)
  const [totalItems, setTotalItems] = useState(0)
  const [totalPages, setTotalPages] = useState(1)
  const [pinnedColumns, setPinnedColumns] = useState<string[]>(config.pinnedColumns || [])
  const [activeTab, setActiveTab] = useState('table')
  const [statsData, setStatsData] = useState<any>(null)

  // Navigation handlers
  const onAdd = () => router.push(config.addRoute)
  const onEdit = (id: string) => router.push(config.editRoute(id))
  const onBulk = () => config.bulkRoute && router.push(config.bulkRoute)

  // Event handlers
  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setCurrentPage(1)
  }

  const handleFilterChange = (filters: string[]) => {
    setSelectedFilters(filters)
    setCurrentPage(1)
  }

  const handleDateFilterChange = (dateFilter: string) => {
    setSelectedDateFilter(dateFilter)
    setCurrentPage(1)
  }

  const handleSortChange = (field: string, order: 'asc' | 'desc') => {
    setSortField(field)
    setSortOrder(order)
    setCurrentPage(1)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleDelete = async (id: string) => {
    if (config.deleteItem) {
    try {
      await config.deleteItem(id)
      toast({
        title: "Success",
        description: "Item deleted successfully",
      })
      // Refresh data after deletion
      fetchData()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete item",
        variant: "destructive",
      })
    }
  } else {
    toast({
      title: "Error",
      description: "Delete function not implemented",
      variant: "destructive",
    })
  }
  }

  const handlePrint = () => {
    window.print()
  }

  // Data fetching
  const fetchData = async () => {
    try {
      setIsLoading(true)
      const response = await config.fetchData({
        page: currentPage,
        search: searchQuery || undefined,
        filters: selectedFilters.length > 0 ? selectedFilters.map(f => ({ field: f, value: true })) : undefined,
        // dateFilter: selectedDateFilter || undefined,
        // sortField: sortField || undefined,
        // sortOrder: sortOrder
      })

      const transformedData = response.items.map(config.transformToTableRow)
      setListData(transformedData)
      setTotalItems(response.total)
      setTotalPages(response.totalPages)
    } catch (error) {
      console.error('Error fetching data:', error)
      toast({
        title: "Error",
        description: "Failed to fetch data",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const fetchStatsData = async () => {
    if (!config.fetchStats) return

    try {
      const stats = await config.fetchStats()
      setStatsData(stats)
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  // Effects
  useEffect(() => {
    fetchStatsData()
  }, [])

  useEffect(() => {
    fetchData()
  }, [searchQuery, selectedFilters, selectedDateFilter, sortField, sortOrder, currentPage])

  return {
    // State
    listData,
    isLoading,
    searchQuery,
    selectedFilters,
    selectedDateFilter,
    sortField,
    sortOrder,
    currentPage,
    pageSize,
    totalItems,
    totalPages,
    pinnedColumns,
    setPinnedColumns,
    activeTab,
    setActiveTab,
    statsData,

    // Handlers
    onAdd,
    onEdit,
    onBulk,
    handleSearch,
    handleFilterChange,
    handleDateFilterChange,
    handleSortChange,
    handlePageChange,
    handleDelete,
    handlePrint,

    // Data fetching
    fetchData,
    fetchStatsData
  }
}
