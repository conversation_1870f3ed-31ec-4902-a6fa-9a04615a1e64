'use client'

export const ERROR_KEYS = {
  // Validation errors
  VALIDATION_FAILED: "api.error.validation_failed",
  INVALID_EMAIL: "api.error.invalid_email",
  INVALID_PHONE: "api.error.invalid_phone",
  INVALID_ID: "api.error.invalid_id",
  INVALID_TOKEN: "api.error.invalid_token",
  INVALID_CREDENTIALS: "api.error.invalid_credentials",
  INVALID_UPDATE_DATA: "api.error.invalid_update_data",
  
  // Resource errors
  NOT_FOUND: "api.error.not_found",
  DUPLICATE_RESOURCE: "api.error.duplicate_resource",
  DUPLICATE_EMAIL: "api.error.duplicate_email",
  DUPLICATE_PHONE: "api.error.duplicate_phone",
  DUPLICATE_NAME: "api.error.duplicate_name",
  
  // Operation errors
  CREATE_FAILED: "api.error.create_failed",
  UPDATE_FAILED: "api.error.update_failed",
  DELETE_FAILED: "api.error.delete_failed",
  FETCH_FAILED: "api.error.fetch_failed",
  LOGIN_FAILED: "api.error.login_failed",
  LOGOUT_FAILED: "api.error.logout_failed",
  
  // Server errors
  INTERNAL_SERVER_ERROR: "api.error.internal_server_error",
  DATABASE_ERROR: "api.error.database_error",
  NETWORK_ERROR: "api.error.network_error",
  
  // Authentication errors
  UNAUTHORIZED: "api.error.unauthorized",
  FORBIDDEN: "api.error.forbidden",
  SESSION_EXPIRED: "api.error.session_expired",
  SESSION_REQUIRED: "api.error.session_required",
  
  // Bulk operation errors
  BULK_IMPORT_FAILED: "api.error.bulk_import_failed",
  BULK_UPDATE_FAILED: "api.error.bulk_update_failed",
  BULK_DELETE_FAILED: "api.error.bulk_delete_failed",
  INVALID_DATA_FORMAT: "api.error.invalid_data_format",
  NO_DATA_PROVIDED: "api.error.no_data_provided",
  
  // Search and filter errors
  SEARCH_KEYWORD_EMPTY: "api.error.search_keyword_empty",
  FILTER_FIELD_EMPTY: "api.error.filter_field_empty",
  SORT_FIELD_EMPTY: "api.error.sort_field_empty",
  INVALID_SORT_DIRECTION: "api.error.invalid_sort_direction",
  
  // File operation errors
  FILE_NOT_FOUND: "api.error.file_not_found",
  FILE_UPLOAD_FAILED: "api.error.file_upload_failed",
  INVALID_FILE_FORMAT: "api.error.invalid_file_format",
  FILE_TOO_LARGE: "api.error.file_too_large"
} as const;
