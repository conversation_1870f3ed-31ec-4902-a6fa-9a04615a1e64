// Search configuration message keys for labels and descriptions
export const SEARCH_CONFIG_KEYS = {
  // Contact filters
  CONTACT_NAME: "api.search_config.contact.name",
  CONTACT_PHONE: "api.search_config.contact.phone",
  CONTACT_EMAIL: "api.search_config.contact.email",
  CONTACT_HAS_EMAIL: "api.search_config.contact.has_email",
  CONTACT_TAGS: "api.search_config.contact.tags",
  CONTACT_STATUS: "api.search_config.contact.status",
  CONTACT_CREATED_BY: "api.search_config.contact.created_by",
  CONTACT_CREATED_DATE: "api.search_config.contact.created_date",
  CONTACT_UPDATED_DATE: "api.search_config.contact.updated_date",
  CONTACT_HAS_PHONE: "api.search_config.contact.has_phone",
  CONTACT_HAS_NOTES: "api.search_config.contact.has_notes",

  // Sales filters
  SALES_CUSTOMER_NAME: "api.search_config.sales.customer_name",
  SALES_CUSTOMER_EMAIL: "api.search_config.sales.customer_email",
  SALES_PRODUCT_NAME: "api.search_config.sales.product_name",
  SALES_AMOUNT: "api.search_config.sales.amount",
  SALES_STATUS: "api.search_config.sales.status",
  SALES_REP: "api.search_config.sales.sales_rep",
  SALES_PAYMENT_METHOD: "api.search_config.sales.payment_method",
  SALES_DATE: "api.search_config.sales.sale_date",
  SALES_DELIVERY_DATE: "api.search_config.sales.delivery_date",
  SALES_IS_RECURRING: "api.search_config.sales.is_recurring",
  SALES_HAS_DISCOUNT: "api.search_config.sales.has_discount",
  SALES_REGION: "api.search_config.sales.region",

  // Sort options
  SORT_NAME: "api.search_config.sort.name",
  SORT_EMAIL: "api.search_config.sort.email",
  SORT_PHONE: "api.search_config.sort.phone",
  SORT_CREATED_DATE: "api.search_config.sort.created_date",
  SORT_UPDATED_DATE: "api.search_config.sort.updated_date",
  SORT_STATUS: "api.search_config.sort.status",

  // Date filter options
  DATE_TODAY: "api.search_config.date.today",
  DATE_YESTERDAY: "api.search_config.date.yesterday",
  DATE_THIS_WEEK: "api.search_config.date.this_week",
  DATE_LAST_WEEK: "api.search_config.date.last_week",
  DATE_THIS_MONTH: "api.search_config.date.this_month",
  DATE_LAST_MONTH: "api.search_config.date.last_month",
  DATE_THIS_YEAR: "api.search_config.date.this_year",
  DATE_LAST_YEAR: "api.search_config.date.last_year",
  DATE_CUSTOM: "api.search_config.date.custom",
  DATE_ALL: "api.search_config.date.all",

  // Date filter descriptions
  DATE_TODAY_DESC: "api.search_config.date.today_desc",
  DATE_YESTERDAY_DESC: "api.search_config.date.yesterday_desc",
  DATE_THIS_WEEK_DESC: "api.search_config.date.this_week_desc",
  DATE_LAST_WEEK_DESC: "api.search_config.date.last_week_desc",
  DATE_THIS_MONTH_DESC: "api.search_config.date.this_month_desc",
  DATE_LAST_MONTH_DESC: "api.search_config.date.last_month_desc",
  DATE_THIS_YEAR_DESC: "api.search_config.date.this_year_desc",
  DATE_LAST_YEAR_DESC: "api.search_config.date.last_year_desc",
  DATE_CUSTOM_DESC: "api.search_config.date.custom_desc",
  DATE_ALL_DESC: "api.search_config.date.all_desc",

  // Status options
  STATUS_ACTIVE: "api.search_config.status.active",
  STATUS_INACTIVE: "api.search_config.status.inactive",
  STATUS_PENDING: "api.search_config.status.pending",
  STATUS_ARCHIVED: "api.search_config.status.archived",
  STATUS_DELETED: "api.search_config.status.deleted",

  // Created by options
  CREATED_BY_SYSTEM: "api.search_config.created_by.system",
  CREATED_BY_ADMIN: "api.search_config.created_by.admin",
  CREATED_BY_USER: "api.search_config.created_by.user",
  CREATED_BY_IMPORT: "api.search_config.created_by.import",
  CREATED_BY_API: "api.search_config.created_by.api",

  // Boolean options
  BOOLEAN_YES: "api.search_config.boolean.yes",
  BOOLEAN_NO: "api.search_config.boolean.no",

  // Placeholders
  PLACEHOLDER_NAME: "api.search_config.placeholder.name",
  PLACEHOLDER_PHONE: "api.search_config.placeholder.phone",
  PLACEHOLDER_EMAIL: "api.search_config.placeholder.email"
} as const;
