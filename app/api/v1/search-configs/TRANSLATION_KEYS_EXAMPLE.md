# Search Configs Translation Keys Example

This document shows how the search configs API now returns translation keys instead of hardcoded strings.

## 🔧 API Response Structure

### Before (Hardcoded Strings):
```json
{
  "status": "success",
  "data": {
    "entity": "contacts",
    "filters": [
      {
        "id": "name",
        "name": "Name",
        "field": "name",
        "type": "string",
        "placeholder": "Search by contact name..."
      }
    ],
    "sortOptions": [
      {
        "value": "name",
        "label": "Name",
        "field": "name",
        "type": "string"
      }
    ],
    "dateFilterOptions": [
      {
        "value": "today",
        "label": "Today",
        "description": "Contacts created today"
      }
    ]
  }
}
```

### After (Translation Keys):
```json
{
  "status": "success",
  "data": {
    "entity": "contacts",
    "filters": [
      {
        "id": "name",
        "name": "search_config.contact.name",
        "field": "name",
        "type": "string",
        "placeholder": "search_config.placeholder.name"
      }
    ],
    "sortOptions": [
      {
        "value": "name",
        "label": "search_config.sort.name",
        "field": "name",
        "type": "string"
      }
    ],
    "dateFilterOptions": [
      {
        "value": "today",
        "label": "search_config.date.today",
        "description": "search_config.date.today_desc"
      }
    ]
  }
}
```

## 🌍 Translation Keys Structure

### Contact Filters:
- `search_config.contact.name` → "Name"
- `search_config.contact.phone` → "Phone Number"
- `search_config.contact.email` → "Email Address"
- `search_config.contact.has_email` → "Has Email"
- `search_config.contact.tags` → "Tags"
- `search_config.contact.status` → "Status"
- `search_config.contact.created_by` → "Created By"
- `search_config.contact.created_date` → "Created Date"
- `search_config.contact.updated_date` → "Last Updated"
- `search_config.contact.has_phone` → "Has Phone Number"
- `search_config.contact.has_notes` → "Has Notes"

### Sort Options:
- `search_config.sort.name` → "Name"
- `search_config.sort.email` → "Email"
- `search_config.sort.phone` → "Phone"
- `search_config.sort.created_date` → "Created Date"
- `search_config.sort.updated_date` → "Updated Date"
- `search_config.sort.status` → "Status"

### Date Filter Options:
- `search_config.date.today` → "Today"
- `search_config.date.yesterday` → "Yesterday"
- `search_config.date.this_week` → "This Week"
- `search_config.date.last_week` → "Last Week"
- `search_config.date.this_month` → "This Month"
- `search_config.date.last_month` → "Last Month"
- `search_config.date.this_year` → "This Year"
- `search_config.date.last_year` → "Last Year"
- `search_config.date.custom` → "Custom Range"
- `search_config.date.all` → "All Time"

### Date Filter Descriptions:
- `search_config.date.today_desc` → "Contacts created today"
- `search_config.date.yesterday_desc` → "Contacts created yesterday"
- `search_config.date.this_week_desc` → "Contacts created this week"
- `search_config.date.last_week_desc` → "Contacts created last week"
- `search_config.date.this_month_desc` → "Contacts created this month"
- `search_config.date.last_month_desc` → "Contacts created last month"
- `search_config.date.this_year_desc` → "Contacts created this year"
- `search_config.date.last_year_desc` → "Contacts created last year"
- `search_config.date.custom_desc` → "Select custom date range"
- `search_config.date.all_desc` → "All contacts"

### Status Options:
- `search_config.status.active` → "Active"
- `search_config.status.inactive` → "Inactive"
- `search_config.status.pending` → "Pending"
- `search_config.status.archived` → "Archived"
- `search_config.status.deleted` → "Deleted"

### Created By Options:
- `search_config.created_by.system` → "System"
- `search_config.created_by.admin` → "Admin"
- `search_config.created_by.user` → "User"
- `search_config.created_by.import` → "Import"
- `search_config.created_by.api` → "API"

### Boolean Options:
- `search_config.boolean.yes` → "Yes"
- `search_config.boolean.no` → "No"

### Placeholders:
- `search_config.placeholder.name` → "Search by contact name..."
- `search_config.placeholder.phone` → "Search by phone number..."
- `search_config.placeholder.email` → "Search by email address..."

## 🚀 Frontend Usage

The frontend can now use these translation keys with the client-side localization hook:

```typescript
import { useLocalization } from "@/hooks/useLocalization/client";

function SearchConfigComponent({ searchConfig }) {
  const { t } = useLocalization();
  
  return (
    <div>
      {searchConfig.filters.map(filter => (
        <div key={filter.id}>
          <label>{t(filter.name)}</label>
          <input placeholder={t(filter.placeholder)} />
        </div>
      ))}
      
      {searchConfig.sortOptions.map(option => (
        <option key={option.value} value={option.value}>
          {t(option.label)}
        </option>
      ))}
      
      {searchConfig.dateFilterOptions.map(option => (
        <div key={option.value} title={t(option.description)}>
          {t(option.label)}
        </div>
      ))}
    </div>
  );
}
```

## 🌐 Adding New Locales

To add support for new languages, create corresponding JSON files:

### `app/api/locales/ja.json` (Japanese):
```json
{
  "api": {
    "search_config": {
      "contact": {
        "name": "名前",
        "phone": "電話番号",
        "email": "メールアドレス"
      },
      "sort": {
        "name": "名前",
        "email": "メール",
        "phone": "電話"
      }
    }
  }
}
```

### `app/api/locales/id.json` (Indonesian):
```json
{
  "api": {
    "search_config": {
      "contact": {
        "name": "Nama",
        "phone": "Nomor Telepon",
        "email": "Alamat Email"
      },
      "sort": {
        "name": "Nama",
        "email": "Email",
        "phone": "Telepon"
      }
    }
  }
}
```

## ✅ Benefits

1. **Consistent Internationalization**: All labels and descriptions can be translated
2. **Client-Side Flexibility**: Frontend can choose which language to display
3. **Maintainable**: Centralized translation management
4. **Type-Safe**: Translation keys are defined in TypeScript constants
5. **Fallback Support**: Graceful degradation when translations are missing

## 🔧 Testing

Test the API endpoint:

```bash
curl "http://localhost:3000/api/v1/search-configs?entity=contacts"
```

The response will now contain translation keys instead of hardcoded English strings.
