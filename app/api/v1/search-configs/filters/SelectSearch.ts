import { <PERSON><PERSON>ilter, MongoSearchQuery, ApiFilterResponse } from './base'

export interface SelectOption {
  value: string
  label: string
  description?: string
  disabled?: boolean
}

export interface SelectSearchOptions {
  options: SelectOption[]
  multiple?: boolean // Allow multiple selections
  caseSensitive?: boolean // Case sensitive matching
  allowCustom?: boolean // Allow custom values not in options
  defaultValue?: string | string[] // Default selected value(s)
}

// ✨ SelectSearch filter for dropdown/select-based filtering
export class SelectSearch extends SearchFilter {
  private options: SelectSearchOptions

  constructor(
    id: string,
    field: string,
    label: string,
    options: SelectSearchOptions
  ) {
    super(id, field, label)
    this.options = options
  }

  // ✨ Build MongoDB query for select search
  build(value: any): MongoSearchQuery | null {
    if (!this.isValidValue(value)) {
      return null
    }

    // Normalize input
    let selectedValues: string[] = []
    
    if (typeof value === 'string') {
      selectedValues = this.options.multiple 
        ? value.split(',').map(v => v.trim()).filter(v => v.length > 0)
        : [value.trim()]
    } else if (Array.isArray(value)) {
      selectedValues = value.filter(v => typeof v === 'string' && v.trim().length > 0)
        .map(v => v.trim())
    } else {
      return null
    }

    if (selectedValues.length === 0) {
      return null
    }

    // Validate against allowed options (if custom values not allowed)
    if (!this.options.allowCustom) {
      const allowedValues = new Set(
        this.options.options.map(opt => 
          this.options.caseSensitive ? opt.value : opt.value.toLowerCase()
        )
      )
      
      selectedValues = selectedValues.filter(v => 
        allowedValues.has(this.options.caseSensitive ? v : v.toLowerCase())
      )
      
      if (selectedValues.length === 0) {
        return null
      }
    }

    // Build MongoDB query
    if (selectedValues.length === 1) {
      // Single value
      return {
        [this.field]: this.options.caseSensitive 
          ? selectedValues[0]
          : { $regex: `^${this.escapeRegex(selectedValues[0])}$`, $options: 'i' }
      }
    } else {
      // Multiple values
      return {
        [this.field]: {
          $in: this.options.caseSensitive 
            ? selectedValues
            : selectedValues.map(v => new RegExp(`^${this.escapeRegex(v)}$`, 'i'))
        }
      }
    }
  }

  // ✨ Helper method to escape regex characters
  escapeRegex(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }

  // ✨ Build API response for frontend
  buildForApiResponse(): ApiFilterResponse {
    const response: ApiFilterResponse = {
      id: this.id,
      name: this.label,
      field: this.field,
      type: this.options.multiple ? 'multiselect' : 'select',
      options: this.options.options.map(opt => ({
        value: opt.value,
        label: opt.label,
        description: opt.description,
        disabled: opt.disabled
      }))
    }

    // Add configuration options
    if (this.options.allowCustom) {
      response.allowCustom = true
    }

    if (this.options.caseSensitive) {
      response.caseSensitive = true
    }

    if (this.options.defaultValue) {
      response.defaultValue = this.options.defaultValue
    }

    return response
  }

  // ✨ Static factory methods for common use cases
  static status(id: string, field: string, label: string, statusOptions: string[]): SelectSearch {
    const options = statusOptions.map(status => ({
      value: status.toLowerCase(),
      label: status
    }))
    
    return new SelectSearch(id, field, label, {
      options,
      caseSensitive: false
    })
  }

  static priority(id: string, field: string, label: string): SelectSearch {
    const options = [
      { value: 'low', label: 'Low' },
      { value: 'medium', label: 'Medium' },
      { value: 'high', label: 'High' },
      { value: 'urgent', label: 'Urgent' }
    ]
    
    return new SelectSearch(id, field, label, { options })
  }

  static category(id: string, field: string, label: string, categories: string[]): SelectSearch {
    const options = categories.map(category => ({
      value: category,
      label: category
    }))
    
    return new SelectSearch(id, field, label, {
      options,
      multiple: true,
      caseSensitive: false
    })
  }

  static boolean(id: string, field: string, label: string, trueLabel = 'Yes', falseLabel = 'No'): SelectSearch {
    const options = [
      { value: 'true', label: trueLabel },
      { value: 'false', label: falseLabel }
    ]
    
    return new SelectSearch(id, field, label, { options })
  }

  static custom(id: string, field: string, label: string, options: SelectOption[], config: Omit<SelectSearchOptions, 'options'> = {}): SelectSearch {
    return new SelectSearch(id, field, label, { options, ...config })
  }
}
