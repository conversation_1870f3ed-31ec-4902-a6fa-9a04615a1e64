import { SearchFilter, MongoSearchQuery, ApiFilterResponse } from './base'

export interface DateRangeSearchOptions {
  allowSingleDate?: boolean // Allow single date (will search entire day)
  defaultRange?: 'today' | 'week' | 'month' | 'year' // Default range if no value provided
  maxRange?: number // Maximum range in days
  minDate?: string // Minimum allowed date (ISO string)
  maxDate?: string // Maximum allowed date (ISO string)
  includeTime?: boolean // Include time in comparison (default: false, compares dates only)
}

// ✨ DateRangeSearch filter for date-based filtering
export class DateRangeSearch extends SearchFilter {
  private options: DateRangeSearchOptions

  constructor(
    id: string,
    field: string,
    label: string,
    options: DateRangeSearchOptions = {}
  ) {
    super(id, field, label)
    this.options = options
  }

  // ✨ Build MongoDB query for date range search
  build(value: any): MongoSearchQuery | null {
    if (!this.isValidValue(value)) {
      return null
    }

    let dateQuery: any = null

    if (typeof value === 'string') {
      // Handle predefined ranges or single date
      if (this.isPredefinedRange(value)) {
        dateQuery = this.buildPredefinedRangeQuery(value)
      } else {
        // Single date
        if (!this.options.allowSingleDate) {
          return null
        }
        dateQuery = this.createDateRangeQuery(value)
      }
    } else if (value && typeof value === 'object') {
      // Handle date range object { from, to }
      dateQuery = this.buildCustomRangeQuery(value)
    }

    if (!dateQuery) {
      return null
    }

    return { [this.field]: dateQuery }
  }

  // ✨ Check if value is a predefined range
  private isPredefinedRange(value: string): boolean {
    const predefinedRanges = ['today', 'yesterday', 'this_week', 'last_week', 'this_month', 'last_month', 'this_year', 'last_year']
    return predefinedRanges.includes(value)
  }

  // ✨ Build query for predefined date ranges
  private buildPredefinedRangeQuery(range: string): any {
    const now = new Date()
    let startDate: Date
    let endDate: Date

    switch (range) {
      case 'today':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
        endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999)
        break

      case 'yesterday':
        const yesterday = new Date(now)
        yesterday.setDate(yesterday.getDate() - 1)
        startDate = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate())
        endDate = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59, 999)
        break

      case 'this_week':
        const startOfWeek = new Date(now)
        startOfWeek.setDate(now.getDate() - now.getDay())
        startDate = new Date(startOfWeek.getFullYear(), startOfWeek.getMonth(), startOfWeek.getDate())
        endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999)
        break

      case 'last_week':
        const lastWeekStart = new Date(now)
        lastWeekStart.setDate(now.getDate() - now.getDay() - 7)
        const lastWeekEnd = new Date(lastWeekStart)
        lastWeekEnd.setDate(lastWeekStart.getDate() + 6)
        startDate = new Date(lastWeekStart.getFullYear(), lastWeekStart.getMonth(), lastWeekStart.getDate())
        endDate = new Date(lastWeekEnd.getFullYear(), lastWeekEnd.getMonth(), lastWeekEnd.getDate(), 23, 59, 59, 999)
        break

      case 'this_month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1)
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999)
        break

      case 'last_month':
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1)
        endDate = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59, 999)
        break

      case 'this_year':
        startDate = new Date(now.getFullYear(), 0, 1)
        endDate = new Date(now.getFullYear(), 11, 31, 23, 59, 59, 999)
        break

      case 'last_year':
        startDate = new Date(now.getFullYear() - 1, 0, 1)
        endDate = new Date(now.getFullYear() - 1, 11, 31, 23, 59, 59, 999)
        break

      default:
        return null
    }

    return { $gte: startDate, $lte: endDate }
  }

  // ✨ Build query for custom date range
  private buildCustomRangeQuery(value: any): any {
    const query: any = {}

    if (value.from) {
      const fromDate = new Date(value.from)
      if (isNaN(fromDate.getTime())) {
        return null
      }

      // Validate against min date
      if (this.options.minDate && fromDate < new Date(this.options.minDate)) {
        return null
      }

      query.$gte = this.options.includeTime ? fromDate : new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate())
    }

    if (value.to) {
      const toDate = new Date(value.to)
      if (isNaN(toDate.getTime())) {
        return null
      }

      // Validate against max date
      if (this.options.maxDate && toDate > new Date(this.options.maxDate)) {
        return null
      }

      query.$lte = this.options.includeTime ? toDate : new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate(), 23, 59, 59, 999)
    }

    // Validate range size
    if (this.options.maxRange && query.$gte && query.$lte) {
      const rangeDays = Math.ceil((query.$lte.getTime() - query.$gte.getTime()) / (1000 * 60 * 60 * 24))
      if (rangeDays > this.options.maxRange) {
        return null
      }
    }

    return Object.keys(query).length > 0 ? query : null
  }

  // ✨ Build API response for frontend
  buildForApiResponse(): ApiFilterResponse {
    const response: ApiFilterResponse = {
      id: this.id,
      name: this.label,
      field: this.field,
      type: 'dateRange',
      options: [
        { value: 'today', label: 'Today' },
        { value: 'yesterday', label: 'Yesterday' },
        { value: 'this_week', label: 'This Week' },
        { value: 'last_week', label: 'Last Week' },
        { value: 'this_month', label: 'This Month' },
        { value: 'last_month', label: 'Last Month' },
        { value: 'this_year', label: 'This Year' },
        { value: 'last_year', label: 'Last Year' },
        { value: 'custom', label: 'Custom Range' }
      ]
    }

    // Add validation rules
    const validation: any = {}

    if (this.options.maxRange) {
      validation.maxRange = this.options.maxRange
    }

    if (this.options.minDate) {
      validation.minDate = this.options.minDate
    }

    if (this.options.maxDate) {
      validation.maxDate = this.options.maxDate
    }

    if (Object.keys(validation).length > 0) {
      response.validation = validation
    }

    // Add configuration options
    response.allowSingleDate = this.options.allowSingleDate || false
    response.includeTime = this.options.includeTime || false
    response.defaultRange = this.options.defaultRange

    return response
  }

  // ✨ Static factory methods for common use cases
  static createdAt(id: string, field: string, label: string, options: DateRangeSearchOptions = {}): DateRangeSearch {
    return new DateRangeSearch(id, field, label, { allowSingleDate: true, ...options })
  }

  static updatedAt(id: string, field: string, label: string, options: DateRangeSearchOptions = {}): DateRangeSearch {
    return new DateRangeSearch(id, field, label, { allowSingleDate: true, ...options })
  }

  static custom(id: string, field: string, label: string, options: DateRangeSearchOptions = {}): DateRangeSearch {
    return new DateRangeSearch(id, field, label, options)
  }
}
