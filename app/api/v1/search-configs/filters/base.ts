// ✨ Base interfaces for search filters
export interface MongoSearchQuery {
  [key: string]: any
}

export interface ApiFilterResponse {
  id: string
  name: string
  field: string
  type: string
  options?: Array<{ value: string; label: string; description?: string; disabled?: boolean }>
  validation?: {
    required?: boolean
    minLength?: number
    maxLength?: number
    pattern?: string
    minItems?: number
    maxItems?: number
    min?: number
    max?: number
    minDate?: string
    maxDate?: string
    maxRange?: number
    domain?: string
    allowedDomains?: string[]
    blockedDomains?: string[]
  }
  // Additional properties for different filter types
  placeholder?: string
  searchType?: string
  requireVerified?: boolean
  checkMultipleFields?: string[]
  matchAll?: boolean
  caseSensitive?: boolean
  exactMatch?: boolean
  allowCustom?: boolean
  defaultValue?: string | string[]
  allowSingleDate?: boolean
  includeTime?: boolean
  defaultRange?: string
}

// ✨ Base SearchFilter class
export abstract class SearchFilter {
  protected id: string
  protected field: string
  protected label: string

  constructor(id: string, field: string, label: string) {
    this.id = id
    this.field = field
    this.label = label
  }

  // ✨ Abstract methods that must be implemented by subclasses
  abstract build(value: any): MongoSearchQuery | null
  abstract buildForApiResponse(): ApiFilterResponse

  // ✨ Getters for common properties
  getId(): string {
    return this.id
  }

  getField(): string {
    return this.field
  }

  getLabel(): string {
    return this.label
  }

  // ✨ Helper method to check if value is valid for filtering
  protected isValidValue(value: any): boolean {
    return value !== null && value !== undefined && value !== ''
  }

  // ✨ Helper method to create regex for case-insensitive search
  protected createRegexQuery(value: string, options: { exact?: boolean; startsWith?: boolean } = {}): any {
    if (options.exact) {
      return { $regex: `^${this.escapeRegex(value)}$`, $options: 'i' }
    }
    
    if (options.startsWith) {
      return { $regex: `^${this.escapeRegex(value)}`, $options: 'i' }
    }
    
    // Default: contains search
    return { $regex: this.escapeRegex(value), $options: 'i' }
  }

  // ✨ Helper method to escape special regex characters
  protected escapeRegex(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }

  // ✨ Helper method to create date range queries
  protected createDateRangeQuery(value: any): any {
    if (typeof value === 'string') {
      // Single date - match the entire day
      const date = new Date(value)
      const startOfDay = new Date(date.setHours(0, 0, 0, 0))
      const endOfDay = new Date(date.setHours(23, 59, 59, 999))
      
      return {
        $gte: startOfDay,
        $lte: endOfDay
      }
    }
    
    if (value && typeof value === 'object') {
      const query: any = {}
      
      if (value.from) {
        query.$gte = new Date(value.from)
      }
      
      if (value.to) {
        query.$lte = new Date(value.to)
      }
      
      return query
    }
    
    return null
  }

  // ✨ Helper method to create number range queries
  protected createNumberRangeQuery(value: any): any {
    if (typeof value === 'number') {
      return value
    }
    
    if (value && typeof value === 'object') {
      const query: any = {}
      
      if (value.min !== undefined) {
        query.$gte = Number(value.min)
      }
      
      if (value.max !== undefined) {
        query.$lte = Number(value.max)
      }
      
      return query
    }
    
    return null
  }
}
