import { <PERSON><PERSON>ilter, MongoSearchQuery, ApiFilterResponse } from './base'

export interface HasEmailOptions {
  requireVerified?: boolean // Only count verified emails
  checkMultipleFields?: string[] // Check multiple email fields
  allowEmpty?: boolean // Allow empty string as "no email"
}

// ✨ HasEmail filter for checking email existence (boolean filter)
export class HasEmail extends SearchFilter {
  private options: HasEmailOptions

  constructor(
    id: string,
    field: string,
    label: string,
    options: HasEmailOptions = {}
  ) {
    super(id, field, label)
    this.options = options
  }

  // ✨ Build MongoDB query for email existence check
  build(value: any): MongoSearchQuery | null {
    // Convert value to boolean
    let hasEmail: boolean
    
    if (typeof value === 'boolean') {
      hasEmail = value
    } else if (typeof value === 'string') {
      const lowerValue = value.toLowerCase()
      if (lowerValue === 'true' || lowerValue === '1' || lowerValue === 'yes') {
        hasEmail = true
      } else if (lowerValue === 'false' || lowerValue === '0' || lowerValue === 'no') {
        hasEmail = false
      } else {
        return null // Invalid boolean value
      }
    } else {
      return null
    }

    // Build the query based on whether we're looking for emails or no emails
    if (hasEmail) {
      return this.buildHasEmailQuery()
    } else {
      return this.buildNoEmailQuery()
    }
  }

  // ✨ Build query for records that have email
  private buildHasEmailQuery(): MongoSearchQuery {
    const fieldsToCheck = this.options.checkMultipleFields || [this.field]
    
    if (fieldsToCheck.length === 1) {
      const field = fieldsToCheck[0]
      const conditions: any[] = [
        { [field]: { $exists: true } },
        { [field]: { $ne: null } },
        { [field]: { $ne: '' } },
        { [field]: { $regex: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ } } // Basic email format
      ]

      // Add verified email requirement if specified
      if (this.options.requireVerified) {
        conditions.push({ [`${field}Verified`]: true })
      }

      return { $and: conditions }
    } else {
      // Check multiple fields - any field can have email
      const fieldConditions = fieldsToCheck.map(field => {
        const conditions: any[] = [
          { [field]: { $exists: true } },
          { [field]: { $ne: null } },
          { [field]: { $ne: '' } },
          { [field]: { $regex: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ } }
        ]

        if (this.options.requireVerified) {
          conditions.push({ [`${field}Verified`]: true })
        }

        return { $and: conditions }
      })

      return { $or: fieldConditions }
    }
  }

  // ✨ Build query for records that don't have email
  private buildNoEmailQuery(): MongoSearchQuery {
    const fieldsToCheck = this.options.checkMultipleFields || [this.field]
    
    if (fieldsToCheck.length === 1) {
      const field = fieldsToCheck[0]
      const conditions: any[] = [
        { [field]: { $exists: false } },
        { [field]: null },
        { [field]: '' }
      ]

      // If we require verified emails, also exclude unverified emails
      if (this.options.requireVerified) {
        conditions.push({ [`${field}Verified`]: { $ne: true } })
      }

      return { $or: conditions }
    } else {
      // Check multiple fields - all fields must not have email
      const fieldConditions = fieldsToCheck.map(field => {
        const conditions: any[] = [
          { [field]: { $exists: false } },
          { [field]: null },
          { [field]: '' }
        ]

        if (this.options.requireVerified) {
          conditions.push({ [`${field}Verified`]: { $ne: true } })
        }

        return { $or: conditions }
      })

      return { $and: fieldConditions }
    }
  }

  // ✨ Build API response for frontend
  buildForApiResponse(): ApiFilterResponse {
    const response: ApiFilterResponse = {
      id: this.id,
      name: this.label,
      field: this.field,
      type: 'boolean',
      options: [
        { value: 'true', label: 'Has Email' },
        { value: 'false', label: 'No Email' }
      ]
    }

    // Add additional metadata
    if (this.options.requireVerified) {
      response.requireVerified = true
      response.options = [
        { value: 'true', label: 'Has Verified Email' },
        { value: 'false', label: 'No Verified Email' }
      ]
    }

    if (this.options.checkMultipleFields) {
      response.checkMultipleFields = this.options.checkMultipleFields
    }

    return response
  }

  // ✨ Static factory methods for common use cases
  static basic(id: string, field: string, label: string): HasEmail {
    return new HasEmail(id, field, label)
  }

  static verified(id: string, field: string, label: string): HasEmail {
    return new HasEmail(id, field, label, { requireVerified: true })
  }

  static multipleFields(id: string, fields: string[], label: string): HasEmail {
    return new HasEmail(id, fields[0], label, { checkMultipleFields: fields })
  }
}
