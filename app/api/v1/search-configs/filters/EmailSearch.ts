import { SearchFilter, MongoSearchQuery, ApiFilterResponse } from './base'

export interface EmailSearchOptions {
  domain?: string // Filter by specific domain
  allowedDomains?: string[] // List of allowed domains
  blockedDomains?: string[] // List of blocked domains
  requireVerified?: boolean // Only search verified emails
  searchType?: 'exact' | 'domain' | 'contains' // Type of email search
}

// ✨ EmailSearch filter for email-specific searches with validation
export class EmailSearch extends SearchFilter {
  private options: EmailSearchOptions
  private emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

  constructor(
    id: string,
    field: string,
    label: string,
    options: EmailSearchOptions = {}
  ) {
    super(id, field, label)
    this.options = options
  }

  // ✨ Build MongoDB query for email search
  build(value: any): MongoSearchQuery | null {
    if (!this.isValidValue(value) || typeof value !== 'string') {
      return null
    }

    const trimmedValue = value.trim().toLowerCase()
    if (trimmedValue.length === 0) {
      return null
    }

    // Build query based on search type
    switch (this.options.searchType) {
      case 'exact':
        return this.buildExactEmailQuery(trimmedValue)
      
      case 'domain':
        return this.buildDomainQuery(trimmedValue)
      
      case 'contains':
      default:
        return this.buildContainsQuery(trimmedValue)
    }
  }

  // ✨ Build exact email match query
  private buildExactEmailQuery(email: string): MongoSearchQuery | null {
    // Validate email format
    if (!this.emailRegex.test(email)) {
      return null
    }

    // Check domain restrictions
    const domain = email.split('@')[1]
    if (!this.isDomainAllowed(domain)) {
      return null
    }

    const query: any = {
      [this.field]: { $regex: `^${this.escapeRegex(email)}$`, $options: 'i' }
    }

    // Add verified email requirement if specified
    if (this.options.requireVerified) {
      query[`${this.field}Verified`] = true
    }

    return query
  }

  // ✨ Build domain-based query
  private buildDomainQuery(domain: string): MongoSearchQuery | null {
    // Remove @ if present
    const cleanDomain = domain.startsWith('@') ? domain.slice(1) : domain
    
    // Basic domain validation
    if (!/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(cleanDomain)) {
      return null
    }

    if (!this.isDomainAllowed(cleanDomain)) {
      return null
    }

    const query: any = {
      [this.field]: { $regex: `@${this.escapeRegex(cleanDomain)}$`, $options: 'i' }
    }

    if (this.options.requireVerified) {
      query[`${this.field}Verified`] = true
    }

    return query
  }

  // ✨ Build contains query (searches in email address)
  private buildContainsQuery(searchTerm: string): MongoSearchQuery | null {
    // Allow partial email searches
    const query: any = {
      [this.field]: { $regex: this.escapeRegex(searchTerm), $options: 'i' }
    }

    if (this.options.requireVerified) {
      query[`${this.field}Verified`] = true
    }

    return query
  }

  // ✨ Check if domain is allowed based on restrictions
  private isDomainAllowed(domain: string): boolean {
    // Check blocked domains
    if (this.options.blockedDomains?.includes(domain)) {
      return false
    }

    // Check specific domain requirement
    if (this.options.domain && domain !== this.options.domain) {
      return false
    }

    // Check allowed domains list
    if (this.options.allowedDomains && !this.options.allowedDomains.includes(domain)) {
      return false
    }

    return true
  }

  // ✨ Helper method to escape regex characters
  escapeRegex(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }

  // ✨ Build API response for frontend
  buildForApiResponse(): ApiFilterResponse {
    const response: ApiFilterResponse = {
      id: this.id,
      name: this.label,
      field: this.field,
      type: 'email'
    }

    // Add domain restrictions as options
    if (this.options.allowedDomains) {
      response.options = this.options.allowedDomains.map(domain => ({
        value: domain,
        label: `@${domain}`
      }))
    }

    // Add validation rules
    const validation: any = {
      pattern: this.emailRegex.source
    }

    if (this.options.domain) {
      validation.domain = this.options.domain
    }

    if (this.options.allowedDomains) {
      validation.allowedDomains = this.options.allowedDomains
    }

    if (this.options.blockedDomains) {
      validation.blockedDomains = this.options.blockedDomains
    }

    response.validation = validation

    // Add search type
    response.searchType = this.options.searchType || 'contains'
    response.requireVerified = this.options.requireVerified || false

    return response
  }

  // ✨ Static factory methods for common use cases
  static exact(id: string, field: string, label: string, options: Omit<EmailSearchOptions, 'searchType'> = {}): EmailSearch {
    return new EmailSearch(id, field, label, { ...options, searchType: 'exact' })
  }

  static domain(id: string, field: string, label: string, options: Omit<EmailSearchOptions, 'searchType'> = {}): EmailSearch {
    return new EmailSearch(id, field, label, { ...options, searchType: 'domain' })
  }

  static contains(id: string, field: string, label: string, options: Omit<EmailSearchOptions, 'searchType'> = {}): EmailSearch {
    return new EmailSearch(id, field, label, { ...options, searchType: 'contains' })
  }

  static verified(id: string, field: string, label: string, options: Omit<EmailSearchOptions, 'requireVerified'> = {}): EmailSearch {
    return new EmailSearch(id, field, label, { ...options, requireVerified: true })
  }
}
