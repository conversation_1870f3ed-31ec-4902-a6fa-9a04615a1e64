import { <PERSON><PERSON>ilter, MongoSearchQuery, ApiFilterResponse } from './base'

export interface StringSearchOptions {
  exact?: boolean // Exact match instead of contains
  startsWith?: boolean // Starts with instead of contains
  minLength?: number // Minimum length validation
  maxLength?: number // Maximum length validation
  pattern?: string // Regex pattern for validation
  placeholder?: string // Placeholder text for UI
}

// ✨ StringSearch filter for text-based searches
export class StringSearch extends SearchFilter {
  private options: StringSearchOptions

  constructor(
    id: string,
    field: string,
    label: string,
    options: StringSearchOptions = {}
  ) {
    super(id, field, label)
    this.options = options
  }

  // ✨ Build MongoDB query for string search
  build(value: any): MongoSearchQuery | null {
    if (!this.isValidValue(value) || typeof value !== 'string') {
      return null
    }

    const trimmedValue = value.trim()
    if (trimmedValue.length === 0) {
      return null
    }

    // Validate against pattern if provided
    if (this.options.pattern) {
      const regex = new RegExp(this.options.pattern)
      if (!regex.test(trimmedValue)) {
        return null
      }
    }

    // Validate length constraints
    if (this.options.minLength && trimmedValue.length < this.options.minLength) {
      return null
    }

    if (this.options.maxLength && trimmedValue.length > this.options.maxLength) {
      return null
    }

    // Build the query based on search type
    return {
      [this.field]: this.createRegexQuery(trimmedValue, {
        exact: this.options.exact,
        startsWith: this.options.startsWith
      })
    }
  }

  // ✨ Build API response for frontend
  buildForApiResponse(): ApiFilterResponse {
    const response: ApiFilterResponse = {
      id: this.id,
      name: this.label,
      field: this.field,
      type: 'string'
    }

    // Add validation rules if any
    const validation: any = {}
    
    if (this.options.minLength) {
      validation.minLength = this.options.minLength
    }
    
    if (this.options.maxLength) {
      validation.maxLength = this.options.maxLength
    }
    
    if (this.options.pattern) {
      validation.pattern = this.options.pattern
    }

    if (Object.keys(validation).length > 0) {
      response.validation = validation
    }

    // Add UI hints
    if (this.options.placeholder) {
      response.placeholder = this.options.placeholder
    }

    if (this.options.exact) {
      response.searchType = 'exact'
    } else if (this.options.startsWith) {
      response.searchType = 'startsWith'
    } else {
      response.searchType = 'contains'
    }

    return response
  }

  // ✨ Static factory methods for common use cases
  static exact(id: string, field: string, label: string, options: Omit<StringSearchOptions, 'exact'> = {}): StringSearch {
    return new StringSearch(id, field, label, { ...options, exact: true })
  }

  static startsWith(id: string, field: string, label: string, options: Omit<StringSearchOptions, 'startsWith'> = {}): StringSearch {
    return new StringSearch(id, field, label, { ...options, startsWith: true })
  }

  static contains(id: string, field: string, label: string, options: StringSearchOptions = {}): StringSearch {
    return new StringSearch(id, field, label, options)
  }
}
