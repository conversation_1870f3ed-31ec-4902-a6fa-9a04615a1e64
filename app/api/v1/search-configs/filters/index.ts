// ✨ Export all search filter classes and types

import { ApiFilterResponse, MongoSearchQuery, SearchFilter } from './base'

// Base classes and interfaces
export { SearchFilter } from './base'
export type { MongoSearchQuery, ApiFilterResponse } from './base'

// String-based filters
export { StringSearch } from './StringSearch'
export type { StringSearchOptions } from './StringSearch'

// Array-based filters
export { ArraySearch } from './ArraySearch'
export type { ArraySearchOptions } from './ArraySearch'

// Email-specific filters
export { EmailSearch } from './EmailSearch'
export type { EmailSearchOptions } from './EmailSearch'

export { HasEmail } from './HasEmail'
export type { HasEmailOptions } from './HasEmail'

// Date-based filters
export { DateRangeSearch } from './DateRangeSearch'
export type { DateRangeSearchOptions } from './DateRangeSearch'

// Select/dropdown filters
export { SelectSearch } from './SelectSearch'
export type { SelectSearchOptions, SelectOption } from './SelectSearch'

// ✨ Utility type for filter collections
export interface FilterCollection {
  [key: string]: SearchFilter
}

// ✨ Helper function to build all filters for API response
export function buildFiltersForApiResponse(filters: SearchFilter[]): ApiFilterResponse[] {
  return filters.map(filter => filter.buildForApiResponse())
}

// ✨ Helper function to build MongoDB queries from user input
export function buildMongoQueries(
  filters: SearchFilter[], 
  userParams: Record<string, any>
): MongoSearchQuery {
  const queries: MongoSearchQuery[] = []

  for (const filter of filters) {
    const filterId = filter.getId()
    const userValue = userParams[filterId]
    
    if (userValue !== undefined && userValue !== null) {
      const query = filter.build(userValue)
      if (query) {
        queries.push(query)
      }
    }
  }

  // Combine all queries with AND logic
  if (queries.length === 0) {
    return {}
  } else if (queries.length === 1) {
    return queries[0]
  } else {
    return { $and: queries }
  }
}

// ✨ Helper function to validate user input against filters
export function validateFilterInput(
  filters: SearchFilter[],
  userParams: Record<string, any>
): { valid: boolean; errors: string[] } {
  const errors: string[] = []
  
  for (const filter of filters) {
    const filterId = filter.getId()
    const userValue = userParams[filterId]
    
    if (userValue !== undefined && userValue !== null) {
      try {
        const query = filter.build(userValue)
        if (query === null) {
          errors.push(`Invalid value for filter '${filterId}': ${userValue}`)
        }
      } catch (error) {
        errors.push(`Error processing filter '${filterId}': ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

// ✨ Helper function to get filter by ID
export function getFilterById(filters: SearchFilter[], id: string): SearchFilter | null {
  return filters.find(filter => filter.getId() === id) || null
}

// ✨ Helper function to get all filter IDs
export function getFilterIds(filters: SearchFilter[]): string[] {
  return filters.map(filter => filter.getId())
}

// ✨ Helper function to create a filter collection from array
export function createFilterCollection(filters: SearchFilter[]): FilterCollection {
  const collection: FilterCollection = {}
  
  for (const filter of filters) {
    collection[filter.getId()] = filter
  }
  
  return collection
}
