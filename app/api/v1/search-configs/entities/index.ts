// ✨ Entity registry for all search configurations

import { contactsSearchConfig, ContactsSearchConfig } from './contacts'
// import { salesSearchConfig, SalesSearchConfig } from './sales'

// ✨ Base interface for search configurations
export interface SearchConfigEntity {
  getFilters(): any[]
  buildForApiResponse(): any
  buildMongoQueries(userParams: Record<string, any>): any
  getFilterById(id: string): any
  getFilterIds(): string[]
  validateInput(userParams: Record<string, any>): { valid: boolean; errors: string[] }
  addFilter(filter: any): void
  removeFilter(id: string): boolean
}

// ✨ Registry of all available search configurations
export class SearchConfigRegistry {
  private static instance: SearchConfigRegistry
  private configs: Map<string, SearchConfigEntity>

  private constructor() {
    this.configs = new Map()
    this.registerDefaultConfigs()
  }

  // ✨ Singleton pattern
  static getInstance(): SearchConfigRegistry {
    if (!SearchConfigRegistry.instance) {
      SearchConfigRegistry.instance = new SearchConfigRegistry()
    }
    return SearchConfigRegistry.instance
  }

  // ✨ Register default configurations
  private registerDefaultConfigs(): void {
    this.configs.set('contacts', contactsSearchConfig)
    // this.configs.set('sales', salesSearchConfig)
  }

  // ✨ Get configuration for an entity
  getConfig(entity: string): SearchConfigEntity | null {
    return this.configs.get(entity.toLowerCase()) || null
  }

  // ✨ Register a new configuration
  registerConfig(entity: string, config: SearchConfigEntity): void {
    this.configs.set(entity.toLowerCase(), config)
  }

  // ✨ Unregister a configuration
  unregisterConfig(entity: string): boolean {
    return this.configs.delete(entity.toLowerCase())
  }

  // ✨ Get all available entities
  getAvailableEntities(): string[] {
    return Array.from(this.configs.keys())
  }

  // ✨ Check if entity exists
  hasEntity(entity: string): boolean {
    return this.configs.has(entity.toLowerCase())
  }

  // ✨ Get all configurations
  getAllConfigs(): Map<string, SearchConfigEntity> {
    return new Map(this.configs)
  }

  // ✨ Clear all configurations
  clear(): void {
    this.configs.clear()
  }

  // ✨ Reset to default configurations
  reset(): void {
    this.clear()
    this.registerDefaultConfigs()
  }
}

// ✨ Export singleton instance
export const searchConfigRegistry = SearchConfigRegistry.getInstance()

// ✨ Export individual configurations
export { contactsSearchConfig, ContactsSearchConfig }
// export { salesSearchConfig, SalesSearchConfig }

// ✨ Helper functions for common operations
export function getSearchConfig(entity: string): SearchConfigEntity | null {
  return searchConfigRegistry.getConfig(entity)
}

export function buildApiResponse(entity: string): any | null {
  const config = searchConfigRegistry.getConfig(entity)
  return config ? config.buildForApiResponse() : null
}

export function buildMongoQueries(entity: string, userParams: Record<string, any>): any | null {
  const config = searchConfigRegistry.getConfig(entity)
  return config ? config.buildMongoQueries(userParams) : null
}

export function validateUserInput(entity: string, userParams: Record<string, any>): { valid: boolean; errors: string[] } | null {
  const config = searchConfigRegistry.getConfig(entity)
  return config ? config.validateInput(userParams) : null
}

export function getAvailableEntities(): string[] {
  return searchConfigRegistry.getAvailableEntities()
}

export function hasEntity(entity: string): boolean {
  return searchConfigRegistry.hasEntity(entity)
}

// ✨ Type exports
// export type { SearchConfigEntity }
