import { useLocalization } from '@/hooks/useLocalization/server'
import {
  StringSearch,
  ArraySearch,
  HasEmail,
  EmailSearch,
  SelectSearch,
  DateRangeSearch,
  SearchFilter,
  buildFiltersForApiResponse,
  buildMongoQueries
} from '../filters'
import { MESSAGE_KEYS } from '@/app/api/message_keys'

// ✨ Composable search configuration for contacts
export class ContactsSearchConfig {
  private filters: SearchFilter[]

  constructor() {
    this.filters = [
      // ✨ String-based searches
      StringSearch.contains('name', 'name', MESSAGE_KEYS.SEARCH_CONFIG.CONTACT_NAME, {
        minLength: 2,
        placeholder: MESSAGE_KEYS.SEARCH_CONFIG.PLACEHOLDER_NAME
      }),

      StringSearch.contains('phone', 'phone', MESSAGE_KEYS.SEARCH_CONFIG.CONTACT_PHONE, {
        pattern: '^[+]?[0-9\\s\\-\\(\\)]+$',
        placeholder: MESSAGE_KEYS.SEARCH_CONFIG.PLACEHOLDER_PHONE
      }),

      // ✨ Email-specific searches
      EmailSearch.contains('email', 'email', MESSAGE_KEYS.SEARCH_CONFIG.CONTACT_EMAIL, {
      }),

      HasEmail.basic('has_email', 'email', MESSAGE_KEYS.SEARCH_CONFIG.CONTACT_HAS_EMAIL),

      // ✨ Array-based searches
      ArraySearch.tags('tags', 'tags', MESSAGE_KEYS.SEARCH_CONFIG.CONTACT_TAGS, [
        'customer', 'lead', 'partner', 'vendor', 'prospect',
        'vip', 'inactive', 'follow-up', 'qualified', 'unqualified'
      ]),

      // ✨ Select-based searches
      SelectSearch.status('status', 'status', MESSAGE_KEYS.SEARCH_CONFIG.CONTACT_STATUS, [
        MESSAGE_KEYS.SEARCH_CONFIG.STATUS_ACTIVE,
        MESSAGE_KEYS.SEARCH_CONFIG.STATUS_INACTIVE,
        MESSAGE_KEYS.SEARCH_CONFIG.STATUS_PENDING,
        MESSAGE_KEYS.SEARCH_CONFIG.STATUS_ARCHIVED,
        MESSAGE_KEYS.SEARCH_CONFIG.STATUS_DELETED
      ]),

      SelectSearch.custom('created_by', 'createdBy', MESSAGE_KEYS.SEARCH_CONFIG.CONTACT_CREATED_BY, [
        { value: 'system', label: MESSAGE_KEYS.SEARCH_CONFIG.CREATED_BY_SYSTEM },
        { value: 'admin', label: MESSAGE_KEYS.SEARCH_CONFIG.CREATED_BY_ADMIN },
        { value: 'user', label: MESSAGE_KEYS.SEARCH_CONFIG.CREATED_BY_USER },
        { value: 'import', label: MESSAGE_KEYS.SEARCH_CONFIG.CREATED_BY_IMPORT },
        { value: 'api', label: MESSAGE_KEYS.SEARCH_CONFIG.CREATED_BY_API }
      ]),

      // ✨ Date-based searches
      DateRangeSearch.createdAt('created_date', 'createdAt', MESSAGE_KEYS.SEARCH_CONFIG.CONTACT_CREATED_DATE, {
        allowSingleDate: true,
        defaultRange: 'today'
      }),

      DateRangeSearch.updatedAt('updated_date', 'updatedAt', MESSAGE_KEYS.SEARCH_CONFIG.CONTACT_UPDATED_DATE, {
        allowSingleDate: true
      }),

      // ✨ Boolean filters
      SelectSearch.boolean('has_phone', 'phone', MESSAGE_KEYS.SEARCH_CONFIG.CONTACT_HAS_PHONE, MESSAGE_KEYS.SEARCH_CONFIG.BOOLEAN_YES, MESSAGE_KEYS.SEARCH_CONFIG.BOOLEAN_NO),

      SelectSearch.boolean('has_notes', 'notes', MESSAGE_KEYS.SEARCH_CONFIG.CONTACT_HAS_NOTES, MESSAGE_KEYS.SEARCH_CONFIG.BOOLEAN_YES, MESSAGE_KEYS.SEARCH_CONFIG.BOOLEAN_NO)
    ]
  }

  // ✨ Get all filters
  getFilters(): SearchFilter[] {
    return this.filters
  }

  // ✨ Build API response for frontend
  buildForApiResponse() {
    return {
      entity: 'contacts',
      filters: buildFiltersForApiResponse(this.filters),
      sortOptions: [
        { value: 'name', label: MESSAGE_KEYS.SEARCH_CONFIG.SORT_NAME, field: 'name', type: 'string' },
        { value: 'email', label: MESSAGE_KEYS.SEARCH_CONFIG.SORT_EMAIL, field: 'email', type: 'string' },
        { value: 'phone', label: MESSAGE_KEYS.SEARCH_CONFIG.SORT_PHONE, field: 'phone', type: 'string' },
        { value: 'createdAt', label: MESSAGE_KEYS.SEARCH_CONFIG.SORT_CREATED_DATE, field: 'createdAt', type: 'date' },
        { value: 'updatedAt', label: MESSAGE_KEYS.SEARCH_CONFIG.SORT_UPDATED_DATE, field: 'updatedAt', type: 'date' },
        { value: 'status', label: MESSAGE_KEYS.SEARCH_CONFIG.SORT_STATUS, field: 'status', type: 'string' }
      ],
      dateFilterOptions: [
        { value: 'today', label: MESSAGE_KEYS.SEARCH_CONFIG.DATE_TODAY, description: MESSAGE_KEYS.SEARCH_CONFIG.DATE_TODAY_DESC },
        { value: 'yesterday', label: MESSAGE_KEYS.SEARCH_CONFIG.DATE_YESTERDAY, description: MESSAGE_KEYS.SEARCH_CONFIG.DATE_YESTERDAY_DESC },
        { value: 'this_week', label: MESSAGE_KEYS.SEARCH_CONFIG.DATE_THIS_WEEK, description: MESSAGE_KEYS.SEARCH_CONFIG.DATE_THIS_WEEK_DESC },
        { value: 'last_week', label: MESSAGE_KEYS.SEARCH_CONFIG.DATE_LAST_WEEK, description: MESSAGE_KEYS.SEARCH_CONFIG.DATE_LAST_WEEK_DESC },
        { value: 'this_month', label: MESSAGE_KEYS.SEARCH_CONFIG.DATE_THIS_MONTH, description: MESSAGE_KEYS.SEARCH_CONFIG.DATE_THIS_MONTH_DESC },
        { value: 'last_month', label: MESSAGE_KEYS.SEARCH_CONFIG.DATE_LAST_MONTH, description: MESSAGE_KEYS.SEARCH_CONFIG.DATE_LAST_MONTH_DESC },
        { value: 'this_year', label: MESSAGE_KEYS.SEARCH_CONFIG.DATE_THIS_YEAR, description: MESSAGE_KEYS.SEARCH_CONFIG.DATE_THIS_YEAR_DESC },
        { value: 'last_year', label: MESSAGE_KEYS.SEARCH_CONFIG.DATE_LAST_YEAR, description: MESSAGE_KEYS.SEARCH_CONFIG.DATE_LAST_YEAR_DESC },
        { value: 'custom', label: MESSAGE_KEYS.SEARCH_CONFIG.DATE_CUSTOM, description: MESSAGE_KEYS.SEARCH_CONFIG.DATE_CUSTOM_DESC },
        { value: 'all', label: MESSAGE_KEYS.SEARCH_CONFIG.DATE_ALL, description: MESSAGE_KEYS.SEARCH_CONFIG.DATE_ALL_DESC }
      ],
      defaultSort: {
        field: 'createdAt',
        direction: 'desc'
      },
      searchableFields: ['name', 'email', 'phone', 'tags']
    }
  }

  // ✨ Build MongoDB queries from user parameters
  buildMongoQueries(userParams: Record<string, any>) {
    return buildMongoQueries(this.filters, userParams)
  }

  // ✨ Get filter by ID
  getFilterById(id: string): SearchFilter | null {
    return this.filters.find(filter => filter.getId() === id) || null
  }

  // ✨ Get all filter IDs
  getFilterIds(): string[] {
    return this.filters.map(filter => filter.getId())
  }

  // ✨ Validate user input
  validateInput(userParams: Record<string, any>): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    for (const filter of this.filters) {
      const filterId = filter.getId()
      const userValue = userParams[filterId]
      
      if (userValue !== undefined && userValue !== null) {
        try {
          const query = filter.build(userValue)
          if (query === null) {
            errors.push(`Invalid value for filter '${filterId}': ${userValue}`)
          }
        } catch (error) {
          errors.push(`Error processing filter '${filterId}': ${error instanceof Error ? error.message : 'Unknown error'}`)
        }
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }

  // ✨ Add custom filter
  addFilter(filter: SearchFilter): void {
    this.filters.push(filter)
  }

  // ✨ Remove filter by ID
  removeFilter(id: string): boolean {
    const index = this.filters.findIndex(filter => filter.getId() === id)
    if (index !== -1) {
      this.filters.splice(index, 1)
      return true
    }
    return false
  }

  // ✨ Replace filter
  replaceFilter(id: string, newFilter: SearchFilter): boolean {
    const index = this.filters.findIndex(filter => filter.getId() === id)
    if (index !== -1) {
      this.filters[index] = newFilter
      return true
    }
    return false
  }
}

// ✨ Export singleton instance
export const contactsSearchConfig = new ContactsSearchConfig()
