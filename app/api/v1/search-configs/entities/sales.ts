import {
  StringSearch,
  ArraySearch,
  SelectSearch,
  DateRangeSearch,
  SearchFilter,
  buildFiltersForApiResponse,
  buildMongoQueries
} from '../filters'

// ✨ Number range search filter (custom for sales amounts)
class NumberRangeSearch extends SearchFilter {
  private minValue?: number
  private maxValue?: number

  constructor(id: string, field: string, label: string, options: { min?: number; max?: number } = {}) {
    super(id, field, label)
    this.minValue = options.min
    this.maxValue = options.max
  }

  build(value: any): any {
    if (!this.isValidValue(value)) {
      return null
    }

    if (typeof value === 'number') {
      return { [this.field]: value }
    }

    if (value && typeof value === 'object') {
      const query: any = {}
      
      if (value.min !== undefined) {
        const min = Number(value.min)
        if (!isNaN(min)) {
          if (this.minValue !== undefined && min < this.minValue) {
            return null
          }
          query.$gte = min
        }
      }
      
      if (value.max !== undefined) {
        const max = Number(value.max)
        if (!isNaN(max)) {
          if (this.maxValue !== undefined && max > this.maxValue) {
            return null
          }
          query.$lte = max
        }
      }
      
      return Object.keys(query).length > 0 ? { [this.field]: query } : null
    }

    return null
  }

  buildForApiResponse(): any {
    return {
      id: this.id,
      name: this.label,
      field: this.field,
      type: 'numberRange',
      validation: {
        min: this.minValue,
        max: this.maxValue
      }
    }
  }
}

// ✨ Composable search configuration for sales
export class SalesSearchConfig {
  private filters: SearchFilter[]

  constructor() {
    this.filters = [
      // ✨ Customer information
      StringSearch.contains('customer_name', 'customerName', 'Customer Name', {
        minLength: 2,
        placeholder: 'Search by customer name...'
      }),

      StringSearch.contains('customer_email', 'customerEmail', 'Customer Email', {
        pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$',
        placeholder: 'Search by customer email...'
      }),

      // ✨ Product information
      StringSearch.contains('product_name', 'productName', 'Product Name', {
        minLength: 2,
        placeholder: 'Search by product name...'
      }),

      ArraySearch.categories('product_category', 'productCategory', 'Product Category', [
        'electronics', 'clothing', 'books', 'home', 'sports', 'automotive', 'health', 'beauty'
      ]),

      // ✨ Sales amount filtering
      new NumberRangeSearch('amount', 'amount', 'Sale Amount', {
        min: 0,
        max: 1000000
      }),

      // ✨ Sales status
      SelectSearch.status('status', 'status', 'Order Status', [
        'Pending', 'Processing', 'Shipped', 'Delivered', 'Cancelled', 'Refunded'
      ]),

      // ✨ Sales representative
      SelectSearch.custom('sales_rep', 'salesRep', 'Sales Representative', [
        { value: 'john_doe', label: 'John Doe' },
        { value: 'jane_smith', label: 'Jane Smith' },
        { value: 'mike_johnson', label: 'Mike Johnson' },
        { value: 'sarah_wilson', label: 'Sarah Wilson' },
        { value: 'david_brown', label: 'David Brown' }
      ]),

      // ✨ Payment method
      SelectSearch.custom('payment_method', 'paymentMethod', 'Payment Method', [
        { value: 'credit_card', label: 'Credit Card' },
        { value: 'debit_card', label: 'Debit Card' },
        { value: 'paypal', label: 'PayPal' },
        { value: 'bank_transfer', label: 'Bank Transfer' },
        { value: 'cash', label: 'Cash' },
        { value: 'check', label: 'Check' }
      ]),

      // ✨ Date-based searches
      DateRangeSearch.createdAt('sale_date', 'createdAt', 'Sale Date', {
        allowSingleDate: true,
        defaultRange: 'this_month'
      }),

      DateRangeSearch.custom('delivery_date', 'deliveryDate', 'Delivery Date', {
        allowSingleDate: true
      }),

      // ✨ Boolean filters
      SelectSearch.boolean('is_recurring', 'isRecurring', 'Recurring Sale', 'Yes', 'No'),

      SelectSearch.boolean('has_discount', 'hasDiscount', 'Has Discount', 'Yes', 'No'),

      // ✨ Region/Location
      ArraySearch.custom('region', 'region', 'Sales Region', {
        allowedValues: ['north', 'south', 'east', 'west', 'central', 'international'],
        exactMatch: true
      })
    ]
  }

  // ✨ Get all filters
  getFilters(): SearchFilter[] {
    return this.filters
  }

  // ✨ Build API response for frontend
  buildForApiResponse() {
    return {
      entity: 'sales',
      filters: buildFiltersForApiResponse(this.filters),
      sortOptions: [
        { value: 'amount', label: 'Amount', field: 'amount', type: 'number' },
        { value: 'customer', label: 'Customer', field: 'customerName', type: 'string' },
        { value: 'product', label: 'Product', field: 'productName', type: 'string' },
        { value: 'status', label: 'Status', field: 'status', type: 'string' },
        { value: 'createdAt', label: 'Sale Date', field: 'createdAt', type: 'date' },
        { value: 'salesRep', label: 'Sales Rep', field: 'salesRep', type: 'string' },
        { value: 'deliveryDate', label: 'Delivery Date', field: 'deliveryDate', type: 'date' }
      ],
      dateFilterOptions: [
        { value: 'today', label: 'Today', description: 'Sales from today' },
        { value: 'yesterday', label: 'Yesterday', description: 'Sales from yesterday' },
        { value: 'this_week', label: 'This Week', description: 'Sales from this week' },
        { value: 'last_week', label: 'Last Week', description: 'Sales from last week' },
        { value: 'this_month', label: 'This Month', description: 'Sales from this month' },
        { value: 'last_month', label: 'Last Month', description: 'Sales from last month' },
        { value: 'this_quarter', label: 'This Quarter', description: 'Sales from this quarter' },
        { value: 'last_quarter', label: 'Last Quarter', description: 'Sales from last quarter' },
        { value: 'this_year', label: 'This Year', description: 'Sales from this year' },
        { value: 'last_year', label: 'Last Year', description: 'Sales from last year' },
        { value: 'custom', label: 'Custom Range', description: 'Select custom date range' },
        { value: 'all', label: 'All Time', description: 'All sales data' }
      ],
      defaultSort: {
        field: 'createdAt',
        direction: 'desc'
      },
      searchableFields: ['customerName', 'customerEmail', 'productName', 'salesRep']
    }
  }

  // ✨ Build MongoDB queries from user parameters
  buildMongoQueries(userParams: Record<string, any>) {
    return buildMongoQueries(this.filters, userParams)
  }

  // ✨ Get filter by ID
  getFilterById(id: string): SearchFilter | null {
    return this.filters.find(filter => filter.getId() === id) || null
  }

  // ✨ Get all filter IDs
  getFilterIds(): string[] {
    return this.filters.map(filter => filter.getId())
  }

  // ✨ Validate user input
  validateInput(userParams: Record<string, any>): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    for (const filter of this.filters) {
      const filterId = filter.getId()
      const userValue = userParams[filterId]
      
      if (userValue !== undefined && userValue !== null) {
        try {
          const query = filter.build(userValue)
          if (query === null) {
            errors.push(`Invalid value for filter '${filterId}': ${userValue}`)
          }
        } catch (error) {
          errors.push(`Error processing filter '${filterId}': ${error instanceof Error ? error.message : 'Unknown error'}`)
        }
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }

  // ✨ Add custom filter
  addFilter(filter: SearchFilter): void {
    this.filters.push(filter)
  }

  // ✨ Remove filter by ID
  removeFilter(id: string): boolean {
    const index = this.filters.findIndex(filter => filter.getId() === id)
    if (index !== -1) {
      this.filters.splice(index, 1)
      return true
    }
    return false
  }
}

// ✨ Export singleton instance
export const salesSearchConfig = new SalesSearchConfig()
