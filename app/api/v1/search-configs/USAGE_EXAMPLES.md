# 🎯 Composable Search Filters - Usage Examples

## Overview

The new composable search filter system allows you to create type-safe, reusable search filters using classes instead of JSON files. Each filter can build MongoDB queries and API responses independently.

## 🏗️ Architecture

```
filters/
├── base.ts              # Base SearchFilter class
├── StringSearch.ts      # String-based searches
├── ArraySearch.ts       # Array/tag searches  
├── EmailSearch.ts       # Email-specific searches
├── HasEmail.ts          # Email existence checks
├── DateRangeSearch.ts   # Date range filtering
├── SelectSearch.ts      # Dropdown selections
└── index.ts             # Exports and utilities

entities/
├── contacts.ts          # Contact search configuration
├── sales.ts             # Sales search configuration
└── index.ts             # Entity registry
```

## 📝 Creating Search Configurations

### Example: Contacts Configuration

```typescript
// entities/contacts.ts
import {
  StringSearch,
  ArraySearch,
  HasEmail,
  EmailSearch,
  SelectSearch,
  DateRangeSearch
} from '../filters'

export class ContactsSearchConfig {
  private filters = [
    // ✨ String searches
    StringSearch.contains('name', 'name', 'Name', {
      minLength: 2,
      placeholder: 'Search by contact name...'
    }),

    StringSearch.contains('phone', 'phone', 'Phone Number', {
      pattern: '^[+]?[0-9\\s\\-\\(\\)]+$',
      placeholder: 'Search by phone number...'
    }),

    // ✨ Email searches
    EmailSearch.contains('email', 'email', 'Email Address'),
    HasEmail.basic('has_email', 'email', 'Has Email'),

    // ✨ Array searches
    ArraySearch.tags('tags', 'tags', 'Tags', [
      'customer', 'lead', 'partner', 'vendor'
    ]),

    // ✨ Select searches
    SelectSearch.status('status', 'status', 'Status', [
      'Active', 'Inactive', 'Pending', 'Archived'
    ]),

    // ✨ Date searches
    DateRangeSearch.createdAt('created_date', 'createdAt', 'Created Date')
  ]

  buildForApiResponse() {
    return {
      entity: 'contacts',
      filters: buildFiltersForApiResponse(this.filters),
      sortOptions: [...],
      dateFilterOptions: [...],
      defaultSort: { field: 'createdAt', direction: 'desc' },
      searchableFields: ['name', 'email', 'phone', 'tags']
    }
  }

  buildMongoQueries(userParams: Record<string, any>) {
    return buildMongoQueries(this.filters, userParams)
  }
}
```

## 🔧 API Usage

### 1. Get Search Configuration

```bash
GET /api/v1/search-configs?entity=contacts
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "entity": "contacts",
    "filters": [
      {
        "id": "name",
        "name": "Name",
        "field": "name",
        "type": "string",
        "searchType": "contains",
        "validation": {
          "minLength": 2
        },
        "placeholder": "Search by contact name..."
      },
      {
        "id": "has_email",
        "name": "Has Email",
        "field": "email",
        "type": "boolean",
        "options": [
          { "value": "true", "label": "Has Email" },
          { "value": "false", "label": "No Email" }
        ]
      }
    ],
    "sortOptions": [...],
    "dateFilterOptions": [...]
  }
}
```

### 2. Build MongoDB Queries

```bash
POST /api/v1/search-configs/build-queries
Content-Type: application/json

{
  "entity": "contacts",
  "params": {
    "name": "john",
    "has_email": "true",
    "tags": "customer,lead",
    "status": "active",
    "created_date": {
      "from": "2024-01-01",
      "to": "2024-12-31"
    }
  }
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "entity": "contacts",
    "queries": {
      "$and": [
        {
          "name": {
            "$regex": "john",
            "$options": "i"
          }
        },
        {
          "$and": [
            { "email": { "$exists": true } },
            { "email": { "$ne": null } },
            { "email": { "$ne": "" } },
            { "email": { "$regex": "^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$" } }
          ]
        },
        {
          "tags": {
            "$in": ["customer", "lead"]
          }
        },
        {
          "status": {
            "$regex": "^active$",
            "$options": "i"
          }
        },
        {
          "createdAt": {
            "$gte": "2024-01-01T00:00:00.000Z",
            "$lte": "2024-12-31T23:59:59.999Z"
          }
        }
      ]
    },
    "userParams": { ... }
  }
}
```

## 🎨 Using in Backend (Contacts API)

```typescript
// app/api/v1/contacts/impl.ts
import { buildMongoQueries } from '../search-configs/entities'

export async function implHandleGetAllContacts(queryParams: any) {
  try {
    // ✨ Build MongoDB queries from user parameters
    const searchQueries = buildMongoQueries('contacts', queryParams)
    
    // ✨ Use in your database query
    const contacts = await contactsBusinessLogic.getAll({
      search: queryParams.search,
      filters: searchQueries, // ✨ Pass the built queries
      page: queryParams.page,
      limit: queryParams.limit,
      sort: queryParams.sort
    })

    return {
      status: 200,
      body: new ResponseWrapper("success", contacts)
    }
  } catch (error) {
    // Error handling...
  }
}
```

## 🎯 Filter Types Available

### StringSearch
```typescript
// Basic contains search
StringSearch.contains('name', 'name', 'Name')

// Exact match
StringSearch.exact('code', 'productCode', 'Product Code')

// Starts with
StringSearch.startsWith('prefix', 'title', 'Title Prefix')

// With validation
StringSearch.contains('phone', 'phone', 'Phone', {
  pattern: '^[+]?[0-9\\s\\-\\(\\)]+$',
  minLength: 10,
  maxLength: 15
})
```

### ArraySearch
```typescript
// Tags with predefined values
ArraySearch.tags('tags', 'tags', 'Tags', [
  'customer', 'lead', 'partner'
])

// Categories
ArraySearch.categories('categories', 'categories', 'Categories', [
  'electronics', 'clothing', 'books'
])

// Keywords (flexible search)
ArraySearch.keywords('keywords', 'keywords', 'Keywords')
```

### EmailSearch
```typescript
// Basic email search
EmailSearch.contains('email', 'email', 'Email')

// Exact email match
EmailSearch.exact('email', 'email', 'Email')

// Domain-specific search
EmailSearch.domain('domain', 'email', 'Email Domain')

// Verified emails only
EmailSearch.verified('verified_email', 'email', 'Verified Email')
```

### SelectSearch
```typescript
// Status dropdown
SelectSearch.status('status', 'status', 'Status', [
  'Active', 'Inactive', 'Pending'
])

// Priority dropdown
SelectSearch.priority('priority', 'priority', 'Priority')

// Boolean selection
SelectSearch.boolean('active', 'isActive', 'Is Active')

// Custom options
SelectSearch.custom('type', 'type', 'Type', [
  { value: 'individual', label: 'Individual' },
  { value: 'company', label: 'Company' }
])
```

### DateRangeSearch
```typescript
// Created date with presets
DateRangeSearch.createdAt('created', 'createdAt', 'Created Date')

// Updated date
DateRangeSearch.updatedAt('updated', 'updatedAt', 'Updated Date')

// Custom date range
DateRangeSearch.custom('custom_date', 'eventDate', 'Event Date', {
  allowSingleDate: true,
  maxRange: 365 // Max 1 year range
})
```

## 🚀 Benefits

1. **Type Safety** - Full TypeScript support
2. **Reusable** - Filters can be shared across entities
3. **Composable** - Mix and match filters as needed
4. **Maintainable** - Each filter is self-contained
5. **Extensible** - Easy to add new filter types
6. **Testable** - Each filter can be tested independently
7. **Consistent** - Same API across all filter types

## 🔄 Migration from JSON

Old JSON approach:
```json
{
  "filters": [
    {
      "id": "name",
      "type": "string",
      "field": "name"
    }
  ]
}
```

New composable approach:
```typescript
StringSearch.contains('name', 'name', 'Name')
```

The new system is more powerful, type-safe, and maintainable!
