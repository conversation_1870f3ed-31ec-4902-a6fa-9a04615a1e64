import { NextRequest, NextResponse } from 'next/server'
import { implHandleGetSearchConfig, implHandleUpdateSearchConfig } from './impl'
import { ERROR_CODES } from "@/app/api/error_codes";
import { MESSAGE_KEYS } from "@/app/api/message_keys";

// ✨ GET endpoint to retrieve search configuration
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const entity = searchParams.get('entity')

    if (!entity) {
      return NextResponse.json(
        {
          status: "failed",
          data: null,
          errors: [MESSAGE_KEYS.ERROR.VALIDATION_FAILED],
          errorCodes: [ERROR_CODES.VALIDATION_FAILED]
        },
        { status: 400 }
      )
    }

    const result = await implHandleGetSearchConfig(entity)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Search configs GET route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: [MESSAGE_KEYS.ERROR.INTERNAL_SERVER_ERROR],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    )
  }
}

// ✨ POST endpoint to update search configuration (for admin use)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { entity, config } = body

    if (!entity || !config) {
      return NextResponse.json(
        {
          status: "failed",
          data: null,
          errors: [MESSAGE_KEYS.ERROR.VALIDATION_FAILED],
          errorCodes: [ERROR_CODES.VALIDATION_FAILED]
        },
        { status: 400 }
      )
    }

    const result = await implHandleUpdateSearchConfig(entity, config)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Search configs POST route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: [MESSAGE_KEYS.ERROR.INTERNAL_SERVER_ERROR],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    )
  }
}
