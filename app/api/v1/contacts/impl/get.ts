import { ContactBusinessLogicInterface } from "@/lib/repositories/contacts/interface";
import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { ERROR_CODES } from "@/app/api/error_codes";

// Get Contact by ID Implementation
export async function implHandleGetContact(
  id: string,
  businessLogic: ContactBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    const contacts = await businessLogic.getById(id);

    if (!contacts) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Contact not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", contacts),
    };
  } catch (error: any) {
    console.error("Get contacts error:", error);

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch contacts. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}
