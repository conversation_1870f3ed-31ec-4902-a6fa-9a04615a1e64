import { ContactBusinessLogicInterface, Contact } from "@/lib/repositories/contacts/interface";
import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { ERROR_CODES } from "@/app/api/error_codes";
import { GetAllResultPaginated, ContactQueryParams } from "./types";
import { ContactResponses, SearchResponses, createSuccessResponse } from "@/lib/utils/api-response-helper";

// Get All Contacts Implementation (handles all, search, and tag filtering)
export async function implHandleGetAllContacts(
  businessLogic: ContactBusinessLogicInterface,
  params?: ContactQueryParams
): Promise<{
  status: number;
  body: ResponseWrapper<GetAllResultPaginated<Contact>>;
}> {
  try {
    // Validate search parameter if provided
    if (params?.search !== undefined) {
      if (!params.search || params.search.trim() === '') {
        return SearchResponses.emptyKeyword();
      }
    }

    // Validate filters if provided
    if (params?.filters && params.filters.length > 0) {
      for (const filter of params.filters) {
        if (!filter.field || filter.field.trim() === '') {
          return SearchResponses.emptyFilterField();
        }
      }
    }

    // Validate sorts if provided
    if (params?.sorts && params.sorts.length > 0) {
      for (const sort of params.sorts) {
        if (!sort.field || sort.field.trim() === '') {
          return SearchResponses.emptySortField();
        }
        if (!['asc', 'desc'].includes(sort.direction)) {
          return SearchResponses.invalidSortDirection();
        }
      }
    }

    // Build query parameters for the repository
    const queryParams: any = {
      includeDeleted: params?.includeDeleted,
      page: params?.page,
      limit: params?.limit,
    };

    // Add search if provided
    if (params?.search) {
      queryParams.search = params.search.trim();
    }

    // Add filters if provided
    if (params?.filters && params.filters.length > 0) {
      queryParams.filters = params.filters;
    }

    // Add sorts if provided
    if (params?.sorts && params.sorts.length > 0) {
      queryParams.sorts = params.sorts;
    }

    const result = await businessLogic.getAll(queryParams);

    return createSuccessResponse<GetAllResultPaginated<Contact>>({ ...result, page: queryParams.page });
  } catch (error: any) {
    console.error("Get contacts error:", error);

    return ContactResponses.fetchFailed();
  }
}
