import { ContactBusinessLogicInterface } from "@/lib/repositories/contacts/interface";
import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { ERROR_CODES } from "@/app/api/error_codes";

// Delete Contact Implementation
export async function implHandleDeleteContact(
  id: string,
  businessLogic: ContactBusinessLogicInterface,
  hardDelete: boolean = false
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    await businessLogic.delete(id, hardDelete);

    return {
      status: 200,
      body: new ResponseWrapper("success", { message: "Contact deleted successfully" }),
    };
  } catch (error: any) {
    console.error("Delete contacts error:", error);

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete contacts. Please try again."],
        [ERROR_CODES.DELETE_FAILED]
      ),
    };
  }
}
