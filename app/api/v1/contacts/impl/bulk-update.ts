import { ContactBusinessLogicInterface } from "@/lib/repositories/contacts/interface";
import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { ERROR_CODES } from "@/app/api/error_codes";
import { BulkUpdateRequest, BulkOperationResult } from "./types";

// Bulk update implementation
export async function implBulkUpdateContacts(
  request: BulkUpdateRequest,
  businessLogic: ContactBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<BulkOperationResult>;
}> {
  try {
    const { data } = request;

    if (!Array.isArray(data)) {
      return {
        status: 400,
        body: new ResponseWrapper<BulkOperationResult>(
          "failed",
          undefined,
          ["Invalid data format - expected array"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    if (data.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper<BulkOperationResult>(
          "failed",
          undefined,
          ["No data provided for update"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate all data and prepare updates
    const validatedUpdates: Array<{ id: string; data: any }> = [];
    const validationErrors: Array<{ row: number; field: string; message: string }> = [];

    for (let i = 0; i < data.length; i++) {
      const contactData = data[i];

      // Validate required fields
      if (!contactData.id) {
        validationErrors.push({
          row: i,
          field: 'id',
          message: 'ID is required for updates'
        });
        continue;
      }

      // Prepare update data (only include fields that exist in ContactUpdateInput)
      const updateData = {
        name: contactData.name,
        phone: contactData.phone,
        email: contactData.email,
        tags: contactData.tags,
        notes: contactData.notes?.map(note => ({
          text: note.text,
          createdAt: note.createdAt || new Date().toISOString()
        }))
      };

      validatedUpdates.push({
        id: contactData.id,
        data: updateData
      });
    }

    const results: BulkOperationResult = {
      total: data.length,
      successful: 0,
      failed: validationErrors.length,
      errors: validationErrors
    };

    // If we have valid updates, perform bulk update
    if (validatedUpdates.length > 0) {
      try {
        const updatedCount = await businessLogic.bulkUpdate(validatedUpdates);
        results.successful = updatedCount;

        // If some updates failed (updatedCount < validatedUpdates.length)
        if (updatedCount < validatedUpdates.length) {
          const failedCount = validatedUpdates.length - updatedCount;
          results.failed += failedCount;
          results.errors.push({
            row: 0,
            field: 'general',
            message: `${failedCount} contacts could not be updated (may not exist or have validation errors)`
          });
        }
      } catch (error: any) {
        // If bulk operation fails, mark all remaining as failed
        results.failed = data.length;
        results.successful = 0;
        results.errors = [{
          row: 0,
          field: 'general',
          message: error instanceof Error ? error.message : 'Bulk update operation failed'
        }];
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", results),
    };
  } catch (error: any) {
    console.error("Bulk update contacts error:", error);

    return {
      status: 500,
      body: new ResponseWrapper<BulkOperationResult>(
        "failed",
        undefined,
        ["Failed to update contacts. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}
