import { promises as fs } from 'fs';
import path from 'path';

type LocaleStrings = {
  [key: string]: string | { [key: string]: any };
};

type LocalizationData = {
  [locale: string]: LocaleStrings;
};

/**
 * Dynamically loads localization JSON files from a directory
 * @param localesPath - Path to the locales directory (relative to project root)
 * @param supportedLocales - Array of supported locale codes (e.g., ['en', 'ja', 'id'])
 * @returns Promise<LocalizationData> - Object with locale data
 */
export async function loadLocalizationFiles(
  localesPath: string,
  supportedLocales: string[] = ['en', 'ja', 'id']
): Promise<LocalizationData> {
  const localizationData: LocalizationData = {};
  
  for (const locale of supportedLocales) {
    try {
      const filePath = path.join(process.cwd(), localesPath, `${locale}.json`);
      const fileContent = await fs.readFile(filePath, 'utf-8');
      localizationData[locale] = JSON.parse(fileContent);
    } catch (error) {
      // If file doesn't exist, create empty object for this locale
      console.warn(`Localization file not found: ${localesPath}/${locale}.json`);
      localizationData[locale] = {};
    }
  }
  
  return localizationData;
}

/**
 * Loads a single localization file
 * @param filePath - Full path to the JSON file
 * @returns Promise<LocaleStrings> - Parsed JSON content
 */
export async function loadSingleLocalizationFile(filePath: string): Promise<LocaleStrings> {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    const fileContent = await fs.readFile(fullPath, 'utf-8');
    return JSON.parse(fileContent);
  } catch (error) {
    console.warn(`Failed to load localization file: ${filePath}`);
    return {};
  }
}

/**
 * Loads localization files with fallback support
 * @param localesPath - Path to the locales directory
 * @param supportedLocales - Array of supported locale codes
 * @param fallbackLocale - Fallback locale (default: 'en')
 * @returns Promise<LocalizationData> - Object with locale data and fallbacks
 */
export async function loadLocalizationWithFallback(
  localesPath: string,
  supportedLocales: string[] = ['en', 'ja', 'id'],
  fallbackLocale: string = 'en'
): Promise<LocalizationData> {
  const localizationData = await loadLocalizationFiles(localesPath, supportedLocales);
  
  // Ensure fallback locale exists
  if (!localizationData[fallbackLocale]) {
    localizationData[fallbackLocale] = {};
  }
  
  // For each locale, merge with fallback if keys are missing
  for (const locale of supportedLocales) {
    if (locale !== fallbackLocale && localizationData[locale]) {
      localizationData[locale] = {
        ...localizationData[fallbackLocale],
        ...localizationData[locale]
      };
    }
  }
  
  return localizationData;
}
