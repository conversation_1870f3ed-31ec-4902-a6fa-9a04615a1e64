import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { MESSAGE_KEYS } from "@/app/api/message_keys";
import { ERROR_CODES } from "@/app/api/error_codes";

/**
 * Helper functions to create standardized API responses with translation keys
 * instead of hardcoded strings
 */

// Success response helpers
export function createSuccessResponse<T>(
  data: T,
  messageKey?: string,
  status: number = 200
): {
  status: number;
  body: ResponseWrapper<T>;
} {
  return {
    status,
    body: new ResponseWrapper(
      "success",
      data,
      undefined,
      undefined,
      messageKey ? [messageKey] : []
    )
  };
}

// Error response helpers
export function createErrorResponse(
  messageKeys: string[],
  errorCodes: string[],
  status: number = 400
): {
  status: number;
  body: ResponseWrapper<any>;
} {
  return {
    status,
    body: new ResponseWrapper(
      "failed",
      undefined,
      messageKeys,
      errorCodes
    )
  };
}

// Validation error response
export function createValidationErrorResponse(
  validationErrors: string[]
): {
  status: number;
  body: ResponseWrapper<any>;
} {
  return createErrorResponse(
    validationErrors,
    [ERROR_CODES.VALIDATION_FAILED],
    400
  );
}

// Not found error response
export function createNotFoundResponse(
  entityType: string = "resource"
): {
  status: number;
  body: ResponseWrapper<any>;
} {
  let messageKey: string = MESSAGE_KEYS.ERROR.NOT_FOUND;

  // Handle specific entity types
  if (entityType === "CONTACT") {
    messageKey = MESSAGE_KEYS.CONTACT.NOT_FOUND;
  } else if (entityType === "MESSAGE_TEMPLATE") {
    messageKey = MESSAGE_KEYS.MESSAGE_TEMPLATE.NOT_FOUND;
  } else if (entityType === "AI_RULE") {
    messageKey = MESSAGE_KEYS.AI_RULE.NOT_FOUND;
  } else if (entityType === "USER") {
    messageKey = MESSAGE_KEYS.USER.NOT_FOUND;
  }

  return createErrorResponse(
    [messageKey],
    [ERROR_CODES.NOT_FOUND],
    404
  );
}

// Duplicate resource error response
export function createDuplicateResourceResponse(
  field?: string
): {
  status: number;
  body: ResponseWrapper<any>;
} {
  let messageKey: string = MESSAGE_KEYS.ERROR.DUPLICATE_RESOURCE;

  if (field) {
    if (field === "phone") {
      messageKey = MESSAGE_KEYS.ERROR.DUPLICATE_PHONE;
    } else if (field === "email") {
      messageKey = MESSAGE_KEYS.ERROR.DUPLICATE_EMAIL;
    } else if (field === "name") {
      messageKey = MESSAGE_KEYS.ERROR.DUPLICATE_NAME;
    }
  }

  return createErrorResponse(
    [messageKey],
    [ERROR_CODES.DUPLICATE_RESOURCE],
    409
  );
}

// Internal server error response
export function createInternalServerErrorResponse(
  messageKey: string = MESSAGE_KEYS.ERROR.INTERNAL_SERVER_ERROR
): {
  status: number;
  body: ResponseWrapper<any>;
} {
  return createErrorResponse(
    [messageKey],
    [ERROR_CODES.INTERNAL_SERVER_ERROR],
    500
  );
}

// Entity-specific response helpers
export const ContactResponses = {
  created: (data: any) => createSuccessResponse(data, MESSAGE_KEYS.CONTACT.CREATED, 201),
  updated: (data: any) => createSuccessResponse(data, MESSAGE_KEYS.CONTACT.UPDATED),
  deleted: () => createSuccessResponse({ message: MESSAGE_KEYS.CONTACT.DELETED }, MESSAGE_KEYS.CONTACT.DELETED),
  notFound: () => createNotFoundResponse("CONTACT"),
  createFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.CONTACT.CREATE_FAILED),
  updateFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.CONTACT.UPDATE_FAILED),
  deleteFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.CONTACT.DELETE_FAILED),
  fetchFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.CONTACT.FETCH_FAILED),
  duplicatePhone: () => createDuplicateResourceResponse("phone"),
  duplicateName: () => createDuplicateResourceResponse("name")
};

export const MessageTemplateResponses = {
  created: (data: any) => createSuccessResponse(data, MESSAGE_KEYS.MESSAGE_TEMPLATE.CREATED, 201),
  updated: (data: any) => createSuccessResponse(data, MESSAGE_KEYS.MESSAGE_TEMPLATE.UPDATED),
  deleted: () => createSuccessResponse({ message: MESSAGE_KEYS.MESSAGE_TEMPLATE.DELETED }, MESSAGE_KEYS.MESSAGE_TEMPLATE.DELETED),
  notFound: () => createNotFoundResponse("MESSAGE_TEMPLATE"),
  createFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.MESSAGE_TEMPLATE.CREATE_FAILED),
  updateFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.MESSAGE_TEMPLATE.UPDATE_FAILED),
  deleteFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.MESSAGE_TEMPLATE.DELETE_FAILED),
  fetchFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.MESSAGE_TEMPLATE.FETCH_FAILED),
  duplicateName: () => createDuplicateResourceResponse("name")
};

export const AiRuleResponses = {
  created: (data: any) => createSuccessResponse(data, MESSAGE_KEYS.AI_RULE.CREATED, 201),
  updated: (data: any) => createSuccessResponse(data, MESSAGE_KEYS.AI_RULE.UPDATED),
  deleted: () => createSuccessResponse({ message: MESSAGE_KEYS.AI_RULE.DELETED }, MESSAGE_KEYS.AI_RULE.DELETED),
  notFound: () => createNotFoundResponse("AI_RULE"),
  createFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.AI_RULE.CREATE_FAILED),
  updateFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.AI_RULE.UPDATE_FAILED),
  deleteFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.AI_RULE.DELETE_FAILED),
  fetchFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.AI_RULE.FETCH_FAILED),
  duplicateName: () => createDuplicateResourceResponse("name")
};

export const UserResponses = {
  created: (data: any) => createSuccessResponse(data, MESSAGE_KEYS.USER.CREATED, 201),
  updated: (data: any) => createSuccessResponse(data, MESSAGE_KEYS.USER.UPDATED),
  deleted: () => createSuccessResponse({ message: MESSAGE_KEYS.USER.DELETED }, MESSAGE_KEYS.USER.DELETED),
  notFound: () => createNotFoundResponse("USER"),
  createFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.USER.CREATE_FAILED),
  updateFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.USER.UPDATE_FAILED),
  deleteFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.USER.DELETE_FAILED),
  fetchFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.USER.FETCH_FAILED),
  duplicateEmail: () => createDuplicateResourceResponse("email")
};

// Search and filter specific responses
export const SearchResponses = {
  emptyKeyword: () => createErrorResponse(
    [MESSAGE_KEYS.ERROR.SEARCH_KEYWORD_EMPTY],
    [ERROR_CODES.VALIDATION_FAILED],
    400
  ),
  emptyFilterField: () => createErrorResponse(
    [MESSAGE_KEYS.ERROR.FILTER_FIELD_EMPTY],
    [ERROR_CODES.VALIDATION_FAILED],
    400
  ),
  emptySortField: () => createErrorResponse(
    [MESSAGE_KEYS.ERROR.SORT_FIELD_EMPTY],
    [ERROR_CODES.VALIDATION_FAILED],
    400
  ),
  invalidSortDirection: () => createErrorResponse(
    [MESSAGE_KEYS.ERROR.INVALID_SORT_DIRECTION],
    [ERROR_CODES.VALIDATION_FAILED],
    400
  )
};

// Bulk operation responses
export const BulkResponses = {
  invalidDataFormat: () => createErrorResponse(
    [MESSAGE_KEYS.ERROR.INVALID_DATA_FORMAT],
    [ERROR_CODES.VALIDATION_FAILED],
    400
  ),
  noDataProvided: () => createErrorResponse(
    [MESSAGE_KEYS.ERROR.NO_DATA_PROVIDED],
    [ERROR_CODES.VALIDATION_FAILED],
    400
  ),
  importFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.ERROR.BULK_IMPORT_FAILED),
  updateFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.ERROR.BULK_UPDATE_FAILED),
  deleteFailed: () => createInternalServerErrorResponse(MESSAGE_KEYS.ERROR.BULK_DELETE_FAILED)
};
