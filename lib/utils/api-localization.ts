import { useLocalization } from "@/hooks/useLocalization/server";
import { promises as fs } from 'fs';
import path from 'path';

type LocaleStrings = {
  [key: string]: string | { [key: string]: any };
};

type LocalizationData = {
  [locale: string]: LocaleStrings;
};

// Cache for loaded localization data to avoid repeated file reads
const localizationCache = new Map<string, LocalizationData>();

/**
 * Load API localization files with caching
 * @param namespace - The namespace for the localization (e.g., 'api', 'contacts')
 * @param localesPath - Path to the locales directory (default: 'app/api/locales')
 * @param supportedLocales - Array of supported locale codes
 * @returns Promise<LocalizationData>
 */
export async function loadApiLocalization(
  namespace: string = 'api',
  localesPath: string = 'app/api/locales',
  supportedLocales: string[] = ['en', 'ja', 'id']
): Promise<LocalizationData> {
  const cacheKey = `${namespace}-${localesPath}`;
  
  // Return cached data if available
  if (localizationCache.has(cacheKey)) {
    return localizationCache.get(cacheKey)!;
  }
  
  const localizationData: LocalizationData = {};
  
  for (const locale of supportedLocales) {
    try {
      const filePath = path.join(process.cwd(), localesPath, `${locale}.json`);
      const fileContent = await fs.readFile(filePath, 'utf-8');
      localizationData[locale] = JSON.parse(fileContent);
    } catch (error) {
      console.warn(`API localization file not found: ${localesPath}/${locale}.json`);
      localizationData[locale] = {};
    }
  }
  
  // Cache the loaded data
  localizationCache.set(cacheKey, localizationData);
  
  return localizationData;
}

/**
 * Get a localized translation function for API responses
 * @param namespace - The namespace for the localization
 * @param localesPath - Path to the locales directory
 * @param supportedLocales - Array of supported locale codes
 * @returns Promise<{ t: function, locale: string }>
 */
export async function getApiLocalization(
  namespace: string = 'api',
  localesPath: string = 'app/api/locales',
  supportedLocales: string[] = ['en', 'ja', 'id']
) {
  const locales = await loadApiLocalization(namespace, localesPath, supportedLocales);
  return await useLocalization(namespace, locales);
}

/**
 * Utility function to create localized API responses
 * @param messageKey - The message key to translate
 * @param namespace - The namespace for the localization
 * @param fallback - Fallback message if translation fails
 * @returns Promise<string>
 */
export async function getLocalizedMessage(
  messageKey: string,
  namespace: string = 'api',
  fallback?: string
): Promise<string> {
  try {
    const { t } = await getApiLocalization(namespace);
    const translated = t(messageKey);
    
    // If translation returns the key itself, it means translation failed
    if (translated === messageKey && fallback) {
      return fallback;
    }
    
    return translated;
  } catch (error) {
    console.error('Failed to get localized message:', error);
    return fallback || messageKey;
  }
}

/**
 * Utility function to create localized error responses
 * @param messageKeys - Array of message keys to translate
 * @param errorCodes - Array of error codes
 * @param namespace - The namespace for the localization
 * @returns Promise<{ messages: string[], errorCodes: string[] }>
 */
export async function getLocalizedErrorResponse(
  messageKeys: string[],
  errorCodes: string[],
  namespace: string = 'api'
): Promise<{ messages: string[], errorCodes: string[] }> {
  try {
    const { t } = await getApiLocalization(namespace);
    const messages = messageKeys.map(key => t(key));
    
    return { messages, errorCodes };
  } catch (error) {
    console.error('Failed to get localized error response:', error);
    // Return the keys as fallback
    return { messages: messageKeys, errorCodes };
  }
}

/**
 * Clear the localization cache (useful for development or when files change)
 */
export function clearApiLocalizationCache(): void {
  localizationCache.clear();
}

/**
 * Preload localization data for better performance
 * @param namespaces - Array of namespaces to preload
 * @param localesPath - Path to the locales directory
 * @param supportedLocales - Array of supported locale codes
 */
export async function preloadApiLocalizations(
  namespaces: string[] = ['api'],
  localesPath: string = 'app/api/locales',
  supportedLocales: string[] = ['en', 'ja', 'id']
): Promise<void> {
  const promises = namespaces.map(namespace => 
    loadApiLocalization(namespace, localesPath, supportedLocales)
  );
  
  await Promise.all(promises);
}

/**
 * Enhanced API localization with template support
 * @param messageKey - The message key to translate
 * @param template - Template variables for interpolation
 * @param namespace - The namespace for the localization
 * @param fallback - Fallback message if translation fails
 * @returns Promise<string>
 */
export async function getLocalizedMessageWithTemplate(
  messageKey: string,
  template: Record<string, string | number>,
  namespace: string = 'api',
  fallback?: string
): Promise<string> {
  try {
    const { t } = await getApiLocalization(namespace);
    const translated = t(messageKey, template);
    
    if (translated === messageKey && fallback) {
      return fallback;
    }
    
    return translated;
  } catch (error) {
    console.error('Failed to get localized message with template:', error);
    return fallback || messageKey;
  }
}
